# 🌱 PlantBuddy

A comprehensive React Native mobile application built with Expo that helps users manage and care for their houseplants. PlantBudd<PERSON> serves as your digital plant care assistant with AI-powered features.

## ✨ Features

- 📱 **Plant Collection Management** - Track your plant collection with photos and details
- 📅 **Smart Task Scheduling** - Automated care reminders (watering, fertilizing, pest checks)
- 🤖 **AI Plant Analysis** - Health assessment and issue detection through camera
- 📊 **Growth Tracking** - Monitor plant development over time
- 🔐 **Secure Authentication** - User accounts with Supabase Auth
- 📱 **Cross-Platform** - Works on iOS, Android, and Web

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Supabase account

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd plant-buddy
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your Supabase credentials:
   - `EXPO_PUBLIC_SUPABASE_URL`: Your Supabase project URL
   - `EXPO_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

4. **Set up Supabase database**
   - Run the migrations in the `supabase/migrations` folder
   - Ensure Row Level Security (RLS) is enabled

5. **Start the development server**
   ```bash
   npm run dev
   ```

## 🏗️ Tech Stack

- **Frontend**: React Native with Expo
- **Backend**: Supabase (PostgreSQL + Auth)
- **Navigation**: Expo Router
- **UI**: Custom components with Lucide icons
- **State Management**: React hooks
- **TypeScript**: Full type safety

## 📁 Project Structure

```
plant-buddy/
├── app/                    # App screens (Expo Router)
│   ├── (tabs)/            # Tab navigation screens
│   ├── auth.tsx           # Authentication
│   ├── camera.tsx         # AI plant analysis
│   └── _layout.tsx        # Root layout
├── components/            # Reusable components
├── services/              # API and data services
├── types/                 # TypeScript type definitions
├── hooks/                 # Custom React hooks
├── assets/                # Images and static files
└── supabase/             # Database migrations
```

## 🔧 Development

### Running Tests
```bash
npm test
```

### Building for Production
```bash
npm run build:web
```

### Linting
```bash
npm run lint
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
