import { PlantAnalysis } from '@/types';
import { savePlantAnalysis } from '@/services/supabaseService';

// Mock ML service for plant health analysis
// In a real app, this would integrate with TensorFlow Lite or a cloud ML service

export const analyzePlantHealth = async (imageUri: string, plantId: string): Promise<PlantAnalysis> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Mock analysis results
  const mockAnalysis: PlantAnalysis = {
    plantId,
    analysisDate: new Date().toISOString(),
    imageUrl: imageUri,
    healthScore: Math.floor(Math.random() * 40) + 60, // Random score between 60-100
    issues: generateMockIssues(),
    recommendations: generateMockRecommendations(),
  };
  
  // Save analysis to database
  try {
    await savePlantAnalysis(mockAnalysis);
  } catch (error) {
    console.error('Failed to save plant analysis:', error);
  }
  
  return mockAnalysis;
};

const generateMockIssues = () => {
  const possibleIssues = [
    {
      type: 'yellowing' as const,
      severity: 'low' as const,
      confidence: 0.75,
      description: 'Some leaf yellowing detected, possibly due to natural aging or slight overwatering.',
    },
    {
      type: 'browning' as const,
      severity: 'medium' as const,
      confidence: 0.65,
      description: 'Brown tips on leaves suggest low humidity or water quality issues.',
    },
    {
      type: 'pests' as const,
      severity: 'low' as const,
      confidence: 0.45,
      description: 'Possible pest activity detected. Check undersides of leaves.',
    },
    {
      type: 'underwatering' as const,
      severity: 'medium' as const,
      confidence: 0.80,
      description: 'Soil appears dry and leaves show signs of water stress.',
    },
    {
      type: 'nutrient-deficiency' as const,
      severity: 'low' as const,
      confidence: 0.60,
      description: 'Possible nitrogen deficiency indicated by pale leaf coloration.',
    },
  ];
  
  // Return 0-2 random issues
  const numberOfIssues = Math.floor(Math.random() * 3);
  const shuffled = possibleIssues.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, numberOfIssues);
};

const generateMockRecommendations = () => {
  const allRecommendations = [
    'Increase watering frequency but ensure good drainage',
    'Improve humidity by misting or using a humidity tray',
    'Move to a location with more indirect sunlight',
    'Consider repotting with fresh, well-draining soil',
    'Apply balanced liquid fertilizer every 2-3 weeks during growing season',
    'Inspect regularly for pests and treat if necessary',
    'Remove any dead or yellowing leaves to prevent disease',
    'Ensure proper air circulation around the plant',
    'Check soil pH and adjust if needed',
    'Consider moving to a slightly cooler location',
  ];
  
  // Return 2-4 random recommendations
  const numberOfRecommendations = Math.floor(Math.random() * 3) + 2;
  const shuffled = allRecommendations.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, numberOfRecommendations);
};

export const identifyPlant = async (imageUri: string): Promise<{
  commonName: string;
  botanicalName: string;
  confidence: number;
  careInstructions: string[];
}> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  const mockPlants = [
    {
      commonName: 'Monstera Deliciosa',
      botanicalName: 'Monstera deliciosa',
      confidence: 0.92,
      careInstructions: [
        'Water when top inch of soil is dry',
        'Prefers bright, indirect light',
        'Loves high humidity',
        'Fertilize monthly during growing season',
      ],
    },
    {
      commonName: 'Snake Plant',
      botanicalName: 'Sansevieria trifasciata',
      confidence: 0.87,
      careInstructions: [
        'Water sparingly, allow soil to dry completely',
        'Tolerates low light conditions',
        'Very drought tolerant',
        'Fertilize 2-3 times per year',
      ],
    },
    {
      commonName: 'Pothos',
      botanicalName: 'Epipremnum aureum',
      confidence: 0.94,
      careInstructions: [
        'Water when soil feels dry',
        'Thrives in indirect light',
        'Easy to propagate',
        'Fertilize monthly in spring/summer',
      ],
    },
    {
      commonName: 'Peace Lily',
      botanicalName: 'Spathiphyllum wallisii',
      confidence: 0.89,
      careInstructions: [
        'Keep soil consistently moist',
        'Prefers shade to partial shade',
        'Enjoys high humidity',
        'Fertilize every 6 weeks during growing season',
      ],
    },
  ];
  
  const randomPlant = mockPlants[Math.floor(Math.random() * mockPlants.length)];
  return randomPlant;
};