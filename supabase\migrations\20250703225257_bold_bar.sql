/*
  # Create plant analyses table

  1. New Tables
    - `plant_analyses`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to profiles)
      - `plant_id` (uuid, foreign key to plants)
      - `analysis_date` (timestamp)
      - `image_url` (text)
      - `health_score` (numeric)
      - `issues` (jsonb)
      - `recommendations` (jsonb)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `plant_analyses` table
    - Add policies for authenticated users to manage their own plant analyses
*/

CREATE TABLE IF NOT EXISTS plant_analyses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  plant_id uuid REFERENCES plants(id) ON DELETE CASCADE NOT NULL,
  analysis_date timestamptz NOT NULL,
  image_url text NOT NULL,
  health_score numeric NOT NULL CHECK (health_score >= 0 AND health_score <= 100),
  issues jsonb DEFAULT '[]'::jsonb,
  recommendations jsonb DEFAULT '[]'::jsonb,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE plant_analyses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own plant analyses"
  ON plant_analyses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own plant analyses"
  ON plant_analyses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own plant analyses"
  ON plant_analyses
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own plant analyses"
  ON plant_analyses
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS plant_analyses_user_id_idx ON plant_analyses(user_id);
CREATE INDEX IF NOT EXISTS plant_analyses_plant_id_idx ON plant_analyses(plant_id);
CREATE INDEX IF NOT EXISTS plant_analyses_analysis_date_idx ON plant_analyses(analysis_date);