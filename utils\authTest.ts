// Simple authentication test utilities
import { signIn, signOut, getCurrentUser } from '@/services/supabaseService';

export const testAuthFlow = async () => {
  console.log('🧪 Starting authentication flow test...');
  
  try {
    // Test 1: Check current user (should be null if logged out)
    console.log('📋 Test 1: Checking current user...');
    const currentUser = await getCurrentUser();
    console.log('Current user:', currentUser ? `${currentUser.email} (${currentUser.id})` : 'None');
    
    // Test 2: Test logout (if user is logged in)
    if (currentUser) {
      console.log('📋 Test 2: Testing logout...');
      await signOut();
      console.log('✅ Logout successful');
      
      // Verify logout worked
      const userAfterLogout = await getCurrentUser();
      console.log('User after logout:', userAfterLogout ? 'Still logged in (ERROR)' : 'Logged out (SUCCESS)');
    } else {
      console.log('📋 Test 2: Skipped (no user logged in)');
    }
    
    console.log('✅ Authentication flow test completed');
    return { success: true };
    
  } catch (error) {
    console.error('❌ Authentication flow test failed:', error);
    return { success: false, error };
  }
};

export const logCurrentAuthState = async () => {
  try {
    const user = await getCurrentUser();
    console.log('🔍 Current Auth State:', {
      isLoggedIn: !!user,
      userId: user?.id || 'none',
      email: user?.email || 'none',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to get auth state:', error);
  }
};
