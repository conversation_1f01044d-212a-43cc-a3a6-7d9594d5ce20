/*
  # Create growth entries table

  1. New Tables
    - `growth_entries`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to profiles)
      - `plant_id` (uuid, foreign key to plants)
      - `date` (timestamp)
      - `type` (enum: new-leaf, new-branch, flowering, fruiting, growth-spurt, other)
      - `description` (text, optional)
      - `height` (numeric, optional)
      - `width` (numeric, optional)
      - `leaf_count` (integer, optional)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `growth_entries` table
    - Add policies for authenticated users to manage their own growth entries
*/

-- Create growth entry type enum
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'growth_entry_type_enum') THEN
    CREATE TYPE growth_entry_type_enum AS ENUM ('new-leaf', 'new-branch', 'flowering', 'fruiting', 'growth-spurt', 'other');
  END IF;
END $$;

CREATE TABLE IF NOT EXISTS growth_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  plant_id uuid REFERENCES plants(id) ON DELETE CASCADE NOT NULL,
  date timestamptz NOT NULL,
  type growth_entry_type_enum NOT NULL,
  description text,
  height numeric,
  width numeric,
  leaf_count integer,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE growth_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own growth entries"
  ON growth_entries
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own growth entries"
  ON growth_entries
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own growth entries"
  ON growth_entries
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own growth entries"
  ON growth_entries
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at timestamp
CREATE TRIGGER handle_growth_entries_updated_at
  BEFORE UPDATE ON growth_entries
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS growth_entries_user_id_idx ON growth_entries(user_id);
CREATE INDEX IF NOT EXISTS growth_entries_plant_id_idx ON growth_entries(plant_id);
CREATE INDEX IF NOT EXISTS growth_entries_date_idx ON growth_entries(date);