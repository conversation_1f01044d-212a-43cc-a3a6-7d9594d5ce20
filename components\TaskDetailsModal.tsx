import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView } from 'react-native';
import { X, ExternalLink, Calendar, Droplets, Scissors, Bug, TrendingUp } from 'lucide-react-native';
import { Task } from '@/types';
import { formatDate } from '@/utils/dateUtils';

interface TaskDetailsModalProps {
  visible: boolean;
  onClose: () => void;
  onOpenTaskDetails: (taskId: string) => void;
  tasks: Task[];
  selectedDate: string;
}

export const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
  visible,
  onClose,
  onOpenTaskDetails,
  tasks,
  selectedDate,
}) => {
  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'water': return <Droplets size={20} color="#3B82F6" />;
      case 'fertilize': return <TrendingUp size={20} color="#F59E0B" />;
      case 'prune': return <Scissors size={20} color="#EF4444" />;
      case 'pest-check': return <Bug size={20} color="#84CC16" />;
      case 'repot': return <Calendar size={20} color="#8B5CF6" />;
      default: return <Calendar size={20} color="#6B7280" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#6B7280';
      default: return '#6B7280';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'High Priority';
      case 'medium': return 'Medium Priority';
      case 'low': return 'Low Priority';
      default: return 'Normal Priority';
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
            <View style={styles.headerContent}>
              <Text style={styles.title}>Tasks for {formatDate(selectedDate)}</Text>
              <Text style={styles.subtitle}>{tasks.length} task{tasks.length !== 1 ? 's' : ''}</Text>
            </View>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {tasks.map((task, index) => (
              <View key={task.id} style={[styles.taskCard, index === tasks.length - 1 && styles.lastTaskCard]}>
                <View style={styles.taskHeader}>
                  <View style={styles.taskIconContainer}>
                    {getTaskIcon(task.type)}
                  </View>
                  <View style={styles.taskInfo}>
                    <Text style={styles.taskTitle}>{task.title}</Text>
                    <Text style={styles.plantName}>{task.plantName}</Text>
                  </View>
                  <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(task.priority) + '20' }]}>
                    <View style={[styles.priorityDot, { backgroundColor: getPriorityColor(task.priority) }]} />
                    <Text style={[styles.priorityText, { color: getPriorityColor(task.priority) }]}>
                      {task.priority?.toUpperCase() || 'MEDIUM'}
                    </Text>
                  </View>
                </View>

                {task.description && (
                  <View style={styles.descriptionContainer}>
                    <Text style={styles.descriptionLabel}>Description:</Text>
                    <Text style={styles.descriptionText}>{task.description}</Text>
                  </View>
                )}

                <View style={styles.taskFooter}>
                  <View style={styles.taskStatus}>
                    <View style={[
                      styles.statusIndicator,
                      { backgroundColor: task.completed ? '#10B981' : '#F59E0B' }
                    ]} />
                    <Text style={[
                      styles.statusText,
                      { color: task.completed ? '#10B981' : '#F59E0B' }
                    ]}>
                      {task.completed ? 'Completed' : 'Pending'}
                    </Text>
                  </View>
                  
                  <TouchableOpacity
                    style={styles.detailsButton}
                    onPress={() => {
                      // Close this modal first, then show task details
                      onClose();
                      // Use setTimeout to ensure the modal closes before showing the dialog
                      setTimeout(() => {
                        onOpenTaskDetails(task.id);
                      }, 100);
                    }}
                  >
                    <ExternalLink size={16} color="#10B981" />
                    <Text style={styles.detailsButtonText}>View Details</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
    padding: 4,
  },
  headerContent: {
    paddingRight: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
    fontFamily: 'Inter-SemiBold',
  },
  subtitle: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
  },
  content: {
    maxHeight: 400,
  },
  taskCard: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  lastTaskCard: {
    borderBottomWidth: 0,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  taskIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  taskInfo: {
    flex: 1,
    marginRight: 12,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
    fontFamily: 'Inter-SemiBold',
  },
  plantName: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
  },
  priorityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  priorityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    fontFamily: 'Inter-SemiBold',
  },
  descriptionContainer: {
    marginBottom: 12,
  },
  descriptionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 4,
    fontFamily: 'Inter-Medium',
  },
  descriptionText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    fontFamily: 'Inter-Regular',
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Inter-Medium',
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
  },
  detailsButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#10B981',
    fontFamily: 'Inter-Medium',
  },
});
