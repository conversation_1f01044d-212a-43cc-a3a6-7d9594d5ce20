import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Settings, Bell, Download, Upload, Share, CircleHelp as HelpCircle, Star, ChevronRight, LogOut } from 'lucide-react-native';
import { getPlants, exportData, importDataFromFile } from '@/services/dataService';
import { signOut, getCurrentUser } from '@/services/supabaseService';
import { useRouter } from 'expo-router';
import { useCustomDialog } from '@/components/CustomDialog';

export default function ProfileScreen() {
  const [plantsCount, setPlantsCount] = useState(0);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [userName, setUserName] = useState('Plant Lover');
  const [userEmail, setUserEmail] = useState('');
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const router = useRouter();
  const { showDialog, hideDialog, DialogComponent } = useCustomDialog();

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      const [plants, user] = await Promise.all([
        getPlants(),
        getCurrentUser(),
      ]);
      
      setPlantsCount(plants.length);
      
      if (user) {
        setUserName(user.user_metadata?.full_name || 'Plant Lover');
        setUserEmail(user.email || '');
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const handleExportData = async () => {
    console.log('Export button clicked');
    try {
      console.log('Starting export...');
      const data = await exportData();
      console.log('Export completed successfully');

      showDialog(
        'Export Complete',
        `Your plant data has been exported and shared successfully!\n\nExported:\n• ${data.plants.length} plants\n• ${data.tasks.length} tasks\n• ${data.growthEntries.length} growth entries\n\nThe file has been saved and shared.`,
        [{ text: 'OK', onPress: () => hideDialog() }],
        'success'
      );
    } catch (error) {
      console.error('Export error:', error);
      showDialog(
        'Error',
        `Failed to export data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        [{ text: 'OK', onPress: () => hideDialog() }],
        'error'
      );
    }
  };

  const handleImportData = async () => {
    console.log('Import button clicked');
    try {
      console.log('Starting import process...');
      const result = await importDataFromFile();
      console.log('Import completed successfully:', result);

      // Reload the profile data to show updated plant count
      await loadProfile();

      showDialog(
        'Import Complete',
        `Your plant data has been imported successfully!\n\nImported:\n• ${result.plants} plants\n\nNote: New plants will get default care tasks. Growth entries can be added manually if needed.`,
        [{ text: 'OK', onPress: () => {
          console.log('Import success dialog closed');
          hideDialog();
        }}],
        'success'
      );
    } catch (error) {
      console.error('Import error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      if (errorMessage.includes('cancelled')) {
        // Don't show error for user cancellation
        console.log('Import cancelled by user');
        return;
      }

      showDialog(
        'Import Failed',
        `Failed to import data: ${errorMessage}\n\nPlease make sure you selected a valid PlantBuddy export file.`,
        [{ text: 'OK', onPress: () => hideDialog() }],
        'error'
      );
    }
  };

  const handleRateApp = () => {
    showDialog(
      'Rate PlantBuddy',
      'Thank you for using PlantBuddy! Would you like to rate us on the App Store?',
      [
        { text: 'Maybe Later', onPress: () => hideDialog(), style: 'cancel' },
        { text: 'Rate Now', onPress: () => {
          console.log('Open App Store');
          hideDialog();
        }},
      ],
      'info'
    );
  };

  const handleGetHelp = () => {
    showDialog(
      'Get Help',
      'Need help? Check our FAQ or contact support.',
      [
        { text: 'FAQ', onPress: () => {
          console.log('Open FAQ');
          hideDialog();
        }},
        { text: 'Contact Support', onPress: () => {
          console.log('Open Support');
          hideDialog();
        }},
        { text: 'Cancel', onPress: () => hideDialog(), style: 'cancel' },
      ],
      'info'
    );
  };

  const handleLogout = () => {
    console.log('Logout button pressed - showing custom dialog');
    setShowLogoutDialog(true);
  };

  const confirmLogout = async () => {
    console.log('User confirmed logout');
    setShowLogoutDialog(false);
    try {
      console.log('Calling signOut...');
      await signOut();
      console.log('SignOut successful, navigating to auth...');
      router.replace('/auth');
    } catch (error) {
      console.error('Logout error:', error);
      // Since Alert doesn't work on this platform, just log the error
      // In a production app, you could create another custom error dialog
    }
  };

  const cancelLogout = () => {
    console.log('User cancelled logout');
    setShowLogoutDialog(false);
  };

  const ProfileSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const ProfileItem = ({ 
    icon, 
    title, 
    subtitle, 
    onPress, 
    showArrow = true,
    rightElement
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    showArrow?: boolean;
    rightElement?: React.ReactNode;
  }) => (
    <TouchableOpacity 
      style={styles.profileItem} 
      onPress={onPress}
      disabled={!onPress}>
      <View style={styles.profileItemLeft}>
        <View style={styles.iconContainer}>
          {icon}
        </View>
        <View style={styles.profileItemText}>
          <Text style={styles.profileItemTitle}>{title}</Text>
          {subtitle && <Text style={styles.profileItemSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightElement || (showArrow && <ChevronRight size={20} color="#6B7280" />)}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <User size={40} color="#10B981" />
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.userName}>{userName}</Text>
              <Text style={styles.userEmail}>{userEmail}</Text>
              <Text style={styles.userStats}>
                {plantsCount} plants • Plant enthusiast
              </Text>
            </View>
          </View>
        </View>

        <ProfileSection title="Plant Care">
          <ProfileItem
            icon={<Download size={20} color="#10B981" />}
            title="Export Data"
            subtitle="Backup your plant data"
            onPress={handleExportData}
          />
          <ProfileItem
            icon={<Upload size={20} color="#10B981" />}
            title="Import Data"
            subtitle="Restore from backup"
            onPress={handleImportData}
          />
          <ProfileItem
            icon={<Share size={20} color="#10B981" />}
            title="Share App"
            subtitle="Tell friends about PlantBuddy"
            onPress={() => console.log('Share app')}
          />
        </ProfileSection>

        <ProfileSection title="Settings">
          <ProfileItem
            icon={<Bell size={20} color="#10B981" />}
            title="Notifications"
            subtitle="Task reminders and alerts"
            showArrow={false}
            rightElement={
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: '#E5E7EB', true: '#10B981' }}
                thumbColor={notificationsEnabled ? '#FFFFFF' : '#FFFFFF'}
              />
            }
          />
          <ProfileItem
            icon={<Settings size={20} color="#10B981" />}
            title="Preferences"
            subtitle="App settings and customization"
            onPress={() => console.log('Open preferences')}
          />
        </ProfileSection>

        <ProfileSection title="Support">
          <ProfileItem
            icon={<Star size={20} color="#10B981" />}
            title="Rate PlantBuddy"
            subtitle="Help us improve"
            onPress={handleRateApp}
          />
          <ProfileItem
            icon={<HelpCircle size={20} color="#10B981" />}
            title="Help & Support"
            subtitle="FAQ and contact info"
            onPress={handleGetHelp}
          />
        </ProfileSection>

        <ProfileSection title="Account">
          <ProfileItem
            icon={<LogOut size={20} color="#EF4444" />}
            title="Logout"
            subtitle="Sign out of your account"
            onPress={handleLogout}
          />
        </ProfileSection>



        <View style={styles.footer}>
          <Text style={styles.footerText}>PlantBuddy v1.0.0</Text>
          <Text style={styles.footerText}>Made with 🌱 for plant lovers</Text>
        </View>
      </ScrollView>

      {/* Custom Logout Confirmation Dialog */}
      {showLogoutDialog && (
        <View style={styles.dialogOverlay}>
          <View style={styles.dialogContainer}>
            <Text style={styles.dialogTitle}>Logout</Text>
            <Text style={styles.dialogMessage}>Are you sure you want to logout?</Text>
            <View style={styles.dialogButtons}>
              <TouchableOpacity style={styles.cancelButton} onPress={cancelLogout}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.logoutButton} onPress={confirmLogout}>
                <Text style={styles.logoutButtonText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Custom Dialog Component */}
      <DialogComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ECFDF5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  userStats: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  profileItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  profileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ECFDF5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  profileItemText: {
    flex: 1,
  },
  profileItemTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 2,
  },
  profileItemSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  footerText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
    marginBottom: 4,
  },
  // Custom dialog styles
  dialogOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  dialogContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    margin: 20,
    minWidth: 280,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  dialogTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  dialogMessage: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 24,
  },
  dialogButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  logoutButton: {
    flex: 1,
    backgroundColor: '#EF4444',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  logoutButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
});