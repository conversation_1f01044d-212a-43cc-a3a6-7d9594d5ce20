export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          location: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          location?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          location?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      plants: {
        Row: {
          id: string
          user_id: string
          name: string
          botanical_name: string | null
          image_url: string
          date_added: string
          last_watered: string | null
          last_repotted: string | null
          location: string
          pot_size: string | null
          soil_type: string | null
          notes: string | null
          health_status: 'healthy' | 'needs-water' | 'needs-attention'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          botanical_name?: string | null
          image_url: string
          date_added: string
          last_watered?: string | null
          last_repotted?: string | null
          location: string
          pot_size?: string | null
          soil_type?: string | null
          notes?: string | null
          health_status?: 'healthy' | 'needs-water' | 'needs-attention'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          botanical_name?: string | null
          image_url?: string
          date_added?: string
          last_watered?: string | null
          last_repotted?: string | null
          location?: string
          pot_size?: string | null
          soil_type?: string | null
          notes?: string | null
          health_status?: 'healthy' | 'needs-water' | 'needs-attention'
          created_at?: string
          updated_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          user_id: string
          plant_id: string
          plant_name: string
          title: string
          description: string | null
          type: 'water' | 'fertilize' | 'repot' | 'prune' | 'pest-check' | 'custom'
          due_date: string
          completed: boolean
          completed_at: string | null
          recurring: boolean
          recurring_interval: number | null
          priority: 'low' | 'medium' | 'high'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          plant_id: string
          plant_name: string
          title: string
          description?: string | null
          type: 'water' | 'fertilize' | 'repot' | 'prune' | 'pest-check' | 'custom'
          due_date: string
          completed?: boolean
          completed_at?: string | null
          recurring?: boolean
          recurring_interval?: number | null
          priority?: 'low' | 'medium' | 'high'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          plant_id?: string
          plant_name?: string
          title?: string
          description?: string | null
          type?: 'water' | 'fertilize' | 'repot' | 'prune' | 'pest-check' | 'custom'
          due_date?: string
          completed?: boolean
          completed_at?: string | null
          recurring?: boolean
          recurring_interval?: number | null
          priority?: 'low' | 'medium' | 'high'
          created_at?: string
          updated_at?: string
        }
      }
      growth_entries: {
        Row: {
          id: string
          user_id: string
          plant_id: string
          date: string
          type: 'new-leaf' | 'new-branch' | 'flowering' | 'fruiting' | 'growth-spurt' | 'other'
          description: string | null
          height: number | null
          width: number | null
          leaf_count: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          plant_id: string
          date: string
          type: 'new-leaf' | 'new-branch' | 'flowering' | 'fruiting' | 'growth-spurt' | 'other'
          description?: string | null
          height?: number | null
          width?: number | null
          leaf_count?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          plant_id?: string
          date?: string
          type?: 'new-leaf' | 'new-branch' | 'flowering' | 'fruiting' | 'growth-spurt' | 'other'
          description?: string | null
          height?: number | null
          width?: number | null
          leaf_count?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      plant_analyses: {
        Row: {
          id: string
          user_id: string
          plant_id: string
          analysis_date: string
          image_url: string
          health_score: number
          issues: Json
          recommendations: Json
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          plant_id: string
          analysis_date: string
          image_url: string
          health_score: number
          issues: Json
          recommendations: Json
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          plant_id?: string
          analysis_date?: string
          image_url?: string
          health_score?: number
          issues?: Json
          recommendations?: Json
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}