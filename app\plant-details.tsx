import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { ArrowLeft, CreditCard as Edit, Calendar, Droplets, Scissors, Bug, Camera, Plus, TrendingUp } from 'lucide-react-native';
import { getPlantById, getTasks, getGrowthEntries, createTask } from '@/services/dataService';
import { Plant, Task, GrowthEntry } from '@/types';
import { useCustomDialog } from '@/components/CustomDialog';
import { formatDate } from '@/utils/dateUtils';
import { useCallback } from 'react';

export default function PlantDetailsScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [plant, setPlant] = useState<Plant | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [growthEntries, setGrowthEntries] = useState<GrowthEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const { showDialog, hideDialog, DialogComponent } = useCustomDialog();

  useEffect(() => {
    if (id) {
      loadPlantDetails(id);
    }
  }, [id]);

  // Reload plant details when screen comes into focus (e.g., after editing)
  useFocusEffect(
    useCallback(() => {
      if (id) {
        console.log('Plant details screen focused - reloading plant data for ID:', id);
        loadPlantDetails(id);
      }
    }, [id])
  );

  const loadPlantDetails = async (plantId: string) => {
    try {
      setIsLoading(true);
      const [plantData, tasksData, growthData] = await Promise.all([
        getPlantById(plantId),
        getTasks(),
        getGrowthEntries(plantId),
      ]);

      setPlant(plantData);
      setTasks(tasksData.filter(task => task.plantId === plantId));
      setGrowthEntries(growthData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load plant details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddTask = () => {
    if (plant) {
      router.push(`/add-task?plantId=${plant.id}`);
    }
  };




  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'water': return <Droplets size={16} color="#3B82F6" />;
      case 'fertilize': return <TrendingUp size={16} color="#F59E0B" />;
      case 'prune': return <Scissors size={16} color="#EF4444" />;
      case 'pest-check': return <Bug size={16} color="#84CC16" />;
      default: return <Calendar size={16} color="#6B7280" />;
    }
  };

  const getUpcomingTasks = () => {
    const today = new Date();
    return tasks
      .filter(task => !task.completed && new Date(task.dueDate) >= today)
      .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
      .slice(0, 3);
  };

  const getRecentGrowth = () => {
    return growthEntries
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 3);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading plant details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!plant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Plant not found</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Plant Details</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => router.push(`/add-plant?id=${plant.id}`)}>
          <Edit size={24} color="#111827" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.plantHeader}>
          <Image source={{ uri: plant.image }} style={styles.plantImage} />
          <View style={styles.plantInfo}>
            <Text style={styles.plantName}>{plant.name}</Text>
            {plant.botanicalName && (
              <Text style={styles.botanicalName}>{plant.botanicalName}</Text>
            )}
            <Text style={styles.plantLocation}>{plant.location}</Text>
            <Text style={styles.plantDate}>
              Added {new Date(plant.dateAdded).toLocaleDateString()}
            </Text>
          </View>
        </View>

        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.actionButton}>
            <Camera size={20} color="#10B981" />
            <Text style={styles.actionText}>Analyze</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Droplets size={20} color="#3B82F6" />
            <Text style={styles.actionText}>Water</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Plus size={20} color="#F59E0B" />
            <Text style={styles.actionText}>Log Growth</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Plant Information</Text>
          <View style={styles.infoCard}>
            {plant.potSize && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Pot Size:</Text>
                <Text style={styles.infoValue}>{plant.potSize}</Text>
              </View>
            )}
            {plant.soilType && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Soil Type:</Text>
                <Text style={styles.infoValue}>{plant.soilType}</Text>
              </View>
            )}
            {plant.lastWatered && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Last Watered:</Text>
                <Text style={styles.infoValue}>
                  {new Date(plant.lastWatered).toLocaleDateString()}
                </Text>
              </View>
            )}
            {plant.lastRepotted && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Last Repotted:</Text>
                <Text style={styles.infoValue}>
                  {new Date(plant.lastRepotted).toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming Tasks</Text>
            <TouchableOpacity style={styles.addTaskButton} onPress={handleAddTask}>
              <Plus size={16} color="#10B981" />
              <Text style={styles.addTaskText}>Add Task</Text>
            </TouchableOpacity>
          </View>
          {getUpcomingTasks().length > 0 ? (
            getUpcomingTasks().map((task) => (
              <View key={task.id} style={styles.taskItem}>
                <View style={styles.taskIcon}>
                  {getTaskIcon(task.type)}
                </View>
                <View style={styles.taskContent}>
                  <Text style={styles.taskTitle}>{task.title}</Text>
                  <Text style={styles.taskDate}>
                    Due: {formatDate(task.dueDate)}
                  </Text>
                </View>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No upcoming tasks</Text>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Growth</Text>
          {getRecentGrowth().length > 0 ? (
            getRecentGrowth().map((entry) => (
              <View key={entry.id} style={styles.growthItem}>
                <View style={styles.growthDate}>
                  <Text style={styles.growthDateText}>
                    {new Date(entry.date).toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.growthContent}>
                  <Text style={styles.growthType}>{entry.type}</Text>
                  {entry.description && (
                    <Text style={styles.growthDescription}>{entry.description}</Text>
                  )}
                </View>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No growth entries yet</Text>
          )}
        </View>

        {plant.notes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <View style={styles.notesCard}>
              <Text style={styles.notesText}>{plant.notes}</Text>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Custom Dialog Component */}
      <DialogComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  content: {
    flex: 1,
  },
  plantHeader: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  plantImage: {
    width: 100,
    height: 100,
    borderRadius: 12,
    marginRight: 16,
  },
  plantInfo: {
    flex: 1,
  },
  plantName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  botanicalName: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  plantLocation: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  plantDate: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  section: {
    paddingHorizontal: 20,
    paddingTop: 20,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addTaskButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  addTaskText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#10B981',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  infoLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  infoValue: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  taskItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  taskIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 4,
  },
  taskDate: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  growthItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  growthDate: {
    marginRight: 12,
  },
  growthDateText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  growthContent: {
    flex: 1,
  },
  growthType: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 4,
  },
  growthDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  notesCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  notesText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
});