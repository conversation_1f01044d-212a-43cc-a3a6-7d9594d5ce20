// Utility functions for debugging authentication issues

export const logAuthState = (context: string, session: any, segments: string[]) => {
  console.log(`[AUTH DEBUG] ${context}:`, {
    hasSession: !!session,
    userId: session?.user?.id || 'none',
    email: session?.user?.email || 'none',
    segments: segments,
    currentRoute: segments.join('/'),
  });
};

export const logAuthEvent = (event: string, session: any) => {
  console.log(`[AUTH EVENT] ${event}:`, {
    hasSession: !!session,
    userId: session?.user?.id || 'none',
    email: session?.user?.email || 'none',
    timestamp: new Date().toISOString(),
  });
};

export const logNavigationAttempt = (from: string, to: string, reason: string) => {
  console.log(`[NAVIGATION] ${from} -> ${to}: ${reason}`);
};
