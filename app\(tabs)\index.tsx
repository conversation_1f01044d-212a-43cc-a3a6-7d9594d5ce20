import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useFocusEffect } from 'expo-router';
import { Plus, Camera, CircleCheck as CheckCircle, CircleAlert as AlertCircle, Clock } from 'lucide-react-native';
import { getPlants, getTasks, completeTask } from '@/services/dataService';
import { Plant, Task } from '@/types';
import { useCustomDialog } from '@/components/CustomDialog';
import { formatDate } from '@/utils/dateUtils';
import { useCallback } from 'react';

export default function HomeScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [todaysTasks, setTodaysTasks] = useState<Task[]>([]);
  const [upcomingTasks, setUpcomingTasks] = useState<Task[]>([]);
  const router = useRouter();
  const { showDialog, DialogComponent } = useCustomDialog();

  useEffect(() => {
    loadData();
  }, []);

  // Reload data when screen comes into focus (e.g., after adding a new plant)
  useFocusEffect(
    useCallback(() => {
      console.log('Home screen focused - reloading data');
      loadData();
    }, [])
  );

  const loadData = async () => {
    try {
      const plantsData = await getPlants();
      const tasksData = await getTasks();
      
      setPlants(plantsData);
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const todayTasks = tasksData.filter(task => {
        const taskDate = new Date(task.dueDate);
        taskDate.setHours(0, 0, 0, 0);
        return taskDate.getTime() === today.getTime() && !task.completed;
      });
      
      const upcomingTasksData = tasksData.filter(task => {
        const taskDate = new Date(task.dueDate);
        taskDate.setHours(0, 0, 0, 0);
        return taskDate.getTime() > today.getTime() && !task.completed;
      }).slice(0, 3);
      
      setTodaysTasks(todayTasks);
      setUpcomingTasks(upcomingTasksData);
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const handleCompleteTask = async (taskId: string) => {
    try {
      await completeTask(taskId);
      loadData();
    } catch (error) {
      showDialog(
        'Error',
        'Failed to complete task',
        [{ text: 'OK', onPress: () => {} }],
        'error'
      );
    }
  };

  const getPlantStatus = (plant: Plant) => {
    const today = new Date();
    const lastWatered = new Date(plant.lastWatered || plant.dateAdded);
    const daysSinceWatered = Math.floor((today.getTime() - lastWatered.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysSinceWatered > 7) return 'needs-water';
    if (daysSinceWatered > 14) return 'needs-attention';
    return 'healthy';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'needs-water': return '#F59E0B';
      case 'needs-attention': return '#EF4444';
      default: return '#10B981';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'needs-water': return <Clock size={16} color="#F59E0B" />;
      case 'needs-attention': return <AlertCircle size={16} color="#EF4444" />;
      default: return <CheckCircle size={16} color="#10B981" />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Good morning! 🌱</Text>
          <Text style={styles.subtitle}>Let's take care of your plants today</Text>
        </View>

        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/add-plant')}>
            <Plus size={24} color="#10B981" />
            <Text style={styles.actionText}>Add Plant</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/camera')}>
            <Camera size={24} color="#10B981" />
            <Text style={styles.actionText}>Analyze</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Tasks</Text>
          {todaysTasks.length > 0 ? (
            todaysTasks.map((task) => (
              <View key={task.id} style={styles.taskItem}>
                <View style={styles.taskContent}>
                  <Text style={styles.taskTitle}>{task.title}</Text>
                  <Text style={styles.taskPlant}>{task.plantName}</Text>
                </View>
                <TouchableOpacity
                  style={styles.completeButton}
                  onPress={() => handleCompleteTask(task.id)}>
                  <CheckCircle size={24} color="#10B981" />
                </TouchableOpacity>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No tasks for today 🎉</Text>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Plants</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {plants.map((plant) => {
              const status = getPlantStatus(plant);
              return (
                <TouchableOpacity
                  key={plant.id}
                  style={styles.plantCard}
                  onPress={() => router.push(`/plant-details?id=${plant.id}`)}>
                  <Image source={{ uri: plant.image }} style={styles.plantImage} />
                  <View style={styles.plantInfo}>
                    <Text style={styles.plantName}>{plant.name}</Text>
                    <View style={styles.statusContainer}>
                      {getStatusIcon(status)}
                      <Text style={[styles.statusText, { color: getStatusColor(status) }]}>
                        {status === 'healthy' ? 'Healthy' : 
                         status === 'needs-water' ? 'Needs Water' : 'Needs Attention'}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Upcoming Tasks</Text>
          {upcomingTasks.length > 0 ? (
            upcomingTasks.map((task) => (
              <View key={task.id} style={styles.upcomingTask}>
                <View style={styles.taskContent}>
                  <Text style={styles.taskTitle}>{task.title}</Text>
                  <Text style={styles.taskPlant}>{task.plantName}</Text>
                </View>
                <Text style={styles.taskDate}>
                  {formatDate(task.dueDate)}
                </Text>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No upcoming tasks</Text>
          )}
        </View>
      </ScrollView>

      {/* Custom Dialog Component */}
      <DialogComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  actionText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  taskItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 4,
  },
  taskPlant: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  completeButton: {
    padding: 4,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    padding: 20,
  },
  plantCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    width: 140,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  plantImage: {
    width: '100%',
    height: 100,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  plantInfo: {
    padding: 12,
  },
  plantName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
  },
  upcomingTask: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  taskDate: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
});