import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { Calendar } from 'react-native-calendars';
import { X, Check } from 'lucide-react-native';

interface DatePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onDateSelect: (date: Date) => void;
  title: string;
  message: string;
}

export const DatePickerModal: React.FC<DatePickerModalProps> = ({
  visible,
  onClose,
  onDateSelect,
  title,
  message,
}) => {
  const [selectedDate, setSelectedDate] = useState<string>('');

  const today = new Date();
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(today.getFullYear() + 1);

  const minDate = today.toISOString().split('T')[0];
  const maxDate = oneYearFromNow.toISOString().split('T')[0];

  const handleDatePress = (day: any) => {
    setSelectedDate(day.dateString);
  };

  const handleConfirm = () => {
    if (selectedDate) {
      const date = new Date(selectedDate);
      onDateSelect(date);
      onClose();
      setSelectedDate(''); // Reset selection
    }
  };

  const handleCancel = () => {
    setSelectedDate(''); // Reset selection
    onClose();
  };

  const getMarkedDates = () => {
    if (!selectedDate) return {};
    
    return {
      [selectedDate]: {
        selected: true,
        selectedColor: '#10B981',
        selectedTextColor: '#FFFFFF',
      },
    };
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={handleCancel}>
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
            <View style={styles.headerContent}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.message}>{message}</Text>
            </View>
          </View>

          <View style={styles.calendarContainer}>
            <Calendar
              onDayPress={handleDatePress}
              markedDates={getMarkedDates()}
              minDate={minDate}
              maxDate={maxDate}
              theme={{
                backgroundColor: '#FFFFFF',
                calendarBackground: '#FFFFFF',
                textSectionTitleColor: '#6B7280',
                selectedDayBackgroundColor: '#10B981',
                selectedDayTextColor: '#FFFFFF',
                todayTextColor: '#10B981',
                dayTextColor: '#111827',
                textDisabledColor: '#D1D5DB',
                dotColor: '#10B981',
                selectedDotColor: '#FFFFFF',
                arrowColor: '#10B981',
                monthTextColor: '#111827',
                indicatorColor: '#10B981',
                textDayFontFamily: 'Inter-Regular',
                textMonthFontFamily: 'Inter-SemiBold',
                textDayHeaderFontFamily: 'Inter-Medium',
                textDayFontSize: 16,
                textMonthFontSize: 18,
                textDayHeaderFontSize: 14,
              }}
            />
          </View>

          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={handleCancel}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.button,
                styles.confirmButton,
                !selectedDate && styles.disabledButton
              ]}
              onPress={handleConfirm}
              disabled={!selectedDate}
            >
              <Check size={16} color="#FFFFFF" />
              <Text style={styles.confirmButtonText}>
                {selectedDate ? `Select ${new Date(selectedDate).toLocaleDateString()}` : 'Select Date'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
    padding: 4,
  },
  headerContent: {
    paddingRight: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
    fontFamily: 'Inter-SemiBold',
  },
  message: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
    fontFamily: 'Inter-Regular',
  },
  calendarContainer: {
    paddingHorizontal: 20,
  },
  buttonsContainer: {
    flexDirection: 'row',
    padding: 20,
    paddingTop: 16,
    gap: 12,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  cancelButtonText: {
    color: '#6B7280',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Inter-Medium',
  },
  confirmButton: {
    backgroundColor: '#10B981',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Inter-Medium',
  },
  disabledButton: {
    backgroundColor: '#D1D5DB',
  },
});
