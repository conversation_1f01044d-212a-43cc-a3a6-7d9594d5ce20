import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Calendar, Droplets, Scissors, Bug, TrendingUp, Check } from 'lucide-react-native';
import { Calendar as CalendarComponent } from 'react-native-calendars';
import { Picker } from '@react-native-picker/picker';
import { getPlantById, createTask } from '@/services/dataService';
import { Plant } from '@/types';
import { useCustomDialog } from '@/components/CustomDialog';
import { createLocalDate, formatLocalDate } from '@/utils/dateUtils';

export default function AddTaskScreen() {
  const router = useRouter();
  const { plantId } = useLocalSearchParams<{ plantId: string }>();
  const [plant, setPlant] = useState<Plant | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { showDialog, DialogComponent } = useCustomDialog();

  // Task form state
  const [taskType, setTaskType] = useState<'water' | 'fertilize' | 'repot' | 'prune' | 'pest-check'>('water');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [recurring, setRecurring] = useState(false);
  const [recurringInterval, setRecurringInterval] = useState(7);

  const today = new Date();
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(today.getFullYear() + 1);

  const minDate = today.toISOString().split('T')[0];
  const maxDate = oneYearFromNow.toISOString().split('T')[0];

  useEffect(() => {
    if (plantId) {
      loadPlant(plantId);
    }
  }, [plantId]);

  useEffect(() => {
    // Set default title based on task type
    const defaultTitles = {
      water: 'Water Plant',
      fertilize: 'Fertilize Plant',
      repot: 'Repot Plant',
      prune: 'Prune Plant',
      'pest-check': 'Pest Check',
    };
    setTitle(defaultTitles[taskType]);
  }, [taskType]);

  const loadPlant = async (id: string) => {
    try {
      const plantData = await getPlantById(id);
      setPlant(plantData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load plant details');
      router.back();
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'water': return <Droplets size={20} color="#3B82F6" />;
      case 'fertilize': return <TrendingUp size={20} color="#F59E0B" />;
      case 'prune': return <Scissors size={20} color="#EF4444" />;
      case 'pest-check': return <Bug size={20} color="#84CC16" />;
      case 'repot': return <Calendar size={20} color="#8B5CF6" />;
      default: return <Calendar size={20} color="#6B7280" />;
    }
  };

  const getMarkedDates = () => {
    if (!selectedDate) return {};
    
    return {
      [selectedDate]: {
        selected: true,
        selectedColor: '#10B981',
        selectedTextColor: '#FFFFFF',
      },
    };
  };

  const handleDatePress = (day: any) => {
    setSelectedDate(day.dateString);
  };

  const handleSave = async () => {
    if (!plant) return;

    if (!selectedDate) {
      showDialog(
        'Missing Information',
        'Please select a due date for the task.',
        [{ text: 'OK', onPress: () => {} }],
        'warning'
      );
      return;
    }

    if (!title.trim()) {
      showDialog(
        'Missing Information',
        'Please enter a task title.',
        [{ text: 'OK', onPress: () => {} }],
        'warning'
      );
      return;
    }

    try {
      setIsLoading(true);

      // Create date in local timezone to avoid timezone conversion issues
      const localDate = createLocalDate(selectedDate);

      const taskData = {
        plantId: plant.id,
        plantName: plant.name,
        title: title.trim(),
        description: description.trim(),
        type: taskType,
        dueDate: localDate.toISOString(),
        completed: false,
        recurring,
        recurringInterval: recurring ? recurringInterval : undefined,
        priority,
      };

      await createTask(taskData);

      // Format the date for display using the utility function
      const displayDate = formatLocalDate(selectedDate);

      showDialog(
        'Task Created',
        `${title} has been scheduled for ${displayDate}`,
        [{
          text: 'OK',
          onPress: () => {
            router.back();
          }
        }],
        'success'
      );
    } catch (error) {
      showDialog(
        'Error',
        'Failed to create task. Please try again.',
        [{ text: 'OK', onPress: () => {} }],
        'error'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!plant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Add Task</Text>
        <TouchableOpacity 
          style={[styles.headerButton, styles.saveButton]} 
          onPress={handleSave}
          disabled={isLoading}
        >
          <Check size={24} color="#10B981" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.plantInfo}>
          <Text style={styles.plantName}>{plant.name}</Text>
          <Text style={styles.plantLocation}>{plant.location}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Task Type</Text>
          <View style={styles.taskTypeGrid}>
            {[
              { type: 'water', label: 'Water' },
              { type: 'fertilize', label: 'Fertilize' },
              { type: 'repot', label: 'Repot' },
              { type: 'prune', label: 'Prune' },
              { type: 'pest-check', label: 'Pest Check' },
            ].map((item) => (
              <TouchableOpacity
                key={item.type}
                style={[
                  styles.taskTypeButton,
                  taskType === item.type && styles.taskTypeButtonActive
                ]}
                onPress={() => setTaskType(item.type as any)}
              >
                {getTaskIcon(item.type)}
                <Text style={[
                  styles.taskTypeText,
                  taskType === item.type && styles.taskTypeTextActive
                ]}>
                  {item.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Task Details</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Title</Text>
            <TextInput
              style={styles.textInput}
              value={title}
              onChangeText={setTitle}
              placeholder="Enter task title"
              placeholderTextColor="#9CA3AF"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Add notes or instructions..."
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Priority</Text>
          <View style={styles.priorityContainer}>
            {[
              { value: 'low', label: 'Low', color: '#6B7280' },
              { value: 'medium', label: 'Medium', color: '#F59E0B' },
              { value: 'high', label: 'High', color: '#EF4444' },
            ].map((item) => (
              <TouchableOpacity
                key={item.value}
                style={[
                  styles.priorityButton,
                  priority === item.value && { backgroundColor: item.color + '20', borderColor: item.color }
                ]}
                onPress={() => setPriority(item.value as any)}
              >
                <View style={[styles.priorityDot, { backgroundColor: item.color }]} />
                <Text style={[
                  styles.priorityText,
                  priority === item.value && { color: item.color }
                ]}>
                  {item.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Due Date</Text>
          <View style={styles.calendarContainer}>
            <CalendarComponent
              onDayPress={handleDatePress}
              markedDates={getMarkedDates()}
              minDate={minDate}
              maxDate={maxDate}
              theme={{
                backgroundColor: '#FFFFFF',
                calendarBackground: '#FFFFFF',
                textSectionTitleColor: '#6B7280',
                selectedDayBackgroundColor: '#10B981',
                selectedDayTextColor: '#FFFFFF',
                todayTextColor: '#10B981',
                dayTextColor: '#111827',
                textDisabledColor: '#D1D5DB',
                arrowColor: '#10B981',
                monthTextColor: '#111827',
                textDayFontFamily: 'Inter-Regular',
                textMonthFontFamily: 'Inter-SemiBold',
                textDayHeaderFontFamily: 'Inter-Medium',
                textDayFontSize: 16,
                textMonthFontSize: 18,
                textDayHeaderFontSize: 14,
              }}
            />
          </View>
        </View>
      </ScrollView>

      <DialogComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerButton: {
    padding: 8,
  },
  saveButton: {
    backgroundColor: '#ECFDF5',
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  plantInfo: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  plantName: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  plantLocation: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 8,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  taskTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  taskTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    gap: 8,
    minWidth: 120,
  },
  taskTypeButtonActive: {
    backgroundColor: '#ECFDF5',
    borderColor: '#10B981',
  },
  taskTypeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  taskTypeTextActive: {
    color: '#10B981',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  priorityContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    gap: 8,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  calendarContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
});
