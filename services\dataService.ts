import AsyncStorage from '@react-native-async-storage/async-storage';
import { Plant, Task, GrowthEntry, AppData } from '@/types';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';
import { v4 as uuidv4 } from 'uuid';
import {
  getPlants as getSupabasePlants,
  getTasks as getSupabaseTasks,
  getGrowthEntries as getSupabaseGrowthEntries,
  getCurrentUser,
  savePlant,
  createTask,
  addGrowthEntry
} from './supabaseService';

// Re-export Supabase service functions for backward compatibility
export {
  getPlants,
  savePlant,
  deletePlant,
  getPlantById,
  getTasks,
  createTask,
  completeTask,
  getGrowthEntries,
  addGrowthEntry,
  initializeExampleData,
} from './supabaseService';

// Proper export functionality
export const exportData = async (): Promise<AppData> => {
  try {
    console.log('Starting data export...');
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    console.log('Fetching data from Supabase...');
    const [plants, tasks, growthEntries] = await Promise.all([
      getSupabasePlants(),
      getSupabaseTasks(),
      getSupabaseGrowthEntries(),
    ]);

    console.log(`Fetched ${plants.length} plants, ${tasks.length} tasks, ${growthEntries.length} growth entries`);

    const exportData: AppData = {
      plants,
      tasks,
      growthEntries,
      user: {
        id: user.id,
        name: user.user_metadata?.full_name || '',
        email: user.email || '',
        joinedDate: user.created_at,
        preferences: {
          notificationsEnabled: true,
          reminderTime: '09:00',
          temperatureUnit: 'celsius',
          language: 'en',
        },
      },
      version: '1.0.0',
      exportDate: new Date().toISOString(),
    };

    const fileName = `plantbuddy-export-${new Date().toISOString().split('T')[0]}.json`;
    const jsonString = JSON.stringify(exportData, null, 2);

    if (Platform.OS === 'web') {
      // Web platform: Use browser download API
      console.log('Using web download for export...');
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      console.log('Web export completed successfully');
    } else {
      // Native platforms: Use Expo FileSystem and Sharing
      console.log('Using native file system for export...');
      const fileUri = FileSystem.documentDirectory + fileName;

      console.log('Writing export file to:', fileUri);
      await FileSystem.writeAsStringAsync(fileUri, jsonString);

      // Share the file
      console.log('Sharing export file...');
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/json',
          dialogTitle: 'Export PlantBuddy Data',
        });
      } else {
        console.log('Sharing not available on this platform');
      }
    }

    return exportData;
  } catch (error) {
    console.error('Export failed:', error);
    throw new Error(`Failed to export data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const importDataFromFile = async (): Promise<{ plants: number; tasks: number; growthEntries: number }> => {
  try {
    console.log('Starting import process...');

    // Pick a file
    const result = await DocumentPicker.getDocumentAsync({
      type: 'application/json',
      copyToCacheDirectory: true,
    });

    if (result.canceled) {
      throw new Error('Import cancelled by user');
    }

    console.log('File selected:', result.assets[0].name);

    // Read the file content (platform-specific)
    let fileContent: string;
    if (Platform.OS === 'web') {
      // Web platform: Use FileReader to read the file
      console.log('Using FileReader for web platform...');
      const response = await fetch(result.assets[0].uri);
      fileContent = await response.text();
    } else {
      // Native platforms: Use Expo FileSystem
      console.log('Using FileSystem for native platform...');
      fileContent = await FileSystem.readAsStringAsync(result.assets[0].uri);
    }
    console.log('File content read, parsing JSON...');

    // Parse the JSON data
    const importData: AppData = JSON.parse(fileContent);

    // Validate the data structure
    if (!importData.plants || !importData.tasks || !importData.growthEntries) {
      throw new Error('Invalid import file format. Missing required data sections.');
    }

    if (!importData.version) {
      throw new Error('Invalid import file format. Missing version information.');
    }

    console.log(`Importing ${importData.plants.length} plants, ${importData.tasks.length} tasks, ${importData.growthEntries.length} growth entries`);

    // Check if user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    let importedPlants = 0;
    let importedTasks = 0;
    let importedGrowthEntries = 0;

    // Import plants
    console.log('Importing plants...');
    for (let i = 0; i < importData.plants.length; i++) {
      const plant = importData.plants[i];
      console.log(`Processing plant ${i + 1}/${importData.plants.length}: ${plant.name}`);

      try {
        // Log the original plant data
        console.log('Original plant data:', {
          name: plant.name,
          botanicalName: plant.botanicalName,
          location: plant.location,
          hasImage: !!plant.image,
        });

        // Create a new plant with a new ID to avoid conflicts
        const newPlant: Plant = {
          id: uuidv4(), // Generate new UUID for the imported plant
          name: plant.name || 'Imported Plant',
          botanicalName: plant.botanicalName || '',
          image: plant.image || 'https://images.pexels.com/photos/1005058/pexels-photo-1005058.jpeg?auto=compress&cs=tinysrgb&w=400',
          dateAdded: new Date().toISOString(), // Use current date for import
          location: plant.location || '',
          potSize: plant.potSize || '',
          soilType: plant.soilType || '',
          notes: (plant.notes || '') + (plant.notes ? '\n\n' : '') + `[Imported from backup on ${new Date().toLocaleDateString()}]`,
          lastWatered: plant.lastWatered || undefined,
          lastRepotted: plant.lastRepotted || undefined,
          photos: plant.photos || [],
          healthStatus: plant.healthStatus || 'healthy',
        };

        console.log('Prepared plant for import:', {
          name: newPlant.name,
          botanicalName: newPlant.botanicalName,
          location: newPlant.location,
        });

        console.log('Calling savePlant with isNewPlant=true...');
        const result = await savePlant(newPlant, true); // Mark as new plant
        console.log('savePlant result:', result);

        importedPlants++;
        console.log(`Successfully imported plant: ${plant.name} (${importedPlants}/${importData.plants.length})`);
      } catch (error) {
        console.error(`Failed to import plant ${plant.name}:`, error);
        console.error('Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        });
      }
    }

    // Import tasks (only pending tasks, skip completed ones)
    console.log('Importing tasks...');
    console.log('Note: Tasks will not be imported as plant IDs have changed. New plants will get default tasks.');
    // Skip task import for now since plant IDs have changed
    // Tasks will be created automatically for new plants by the savePlant function

    // Import growth entries
    console.log('Importing growth entries...');
    console.log('Note: Growth entries will not be imported as plant IDs have changed.');
    // Skip growth entry import for now since plant IDs have changed
    // Users can manually add growth entries for their imported plants

    console.log(`Import completed: ${importedPlants} plants, ${importedTasks} tasks, ${importedGrowthEntries} growth entries`);

    return {
      plants: importedPlants,
      tasks: importedTasks,
      growthEntries: importedGrowthEntries,
    };
  } catch (error) {
    console.error('Import failed:', error);
    throw error;
  }
};

// Legacy function for backward compatibility
export const importData = async (data: AppData) => {
  console.log('Legacy importData called, use importDataFromFile instead');
  throw new Error('Use importDataFromFile() instead');
};