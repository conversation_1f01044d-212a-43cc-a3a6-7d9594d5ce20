/*
  # Complete PlantBuddy Database Schema

  1. New Tables
    - `profiles` - User profile information
    - `plants` - Plant records with care information
    - `tasks` - Plant care tasks and reminders
    - `growth_entries` - Plant growth tracking
    - `plant_analyses` - AI-powered plant health analysis

  2. Custom Types
    - `health_status_enum` - Plant health status values
    - `task_type_enum` - Task type categories
    - `priority_enum` - Task priority levels
    - `growth_entry_type_enum` - Growth entry types

  3. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to access their own data
    - Automatic profile creation on user registration

  4. Performance
    - Indexes on frequently queried columns
    - Automatic timestamp updates
*/

-- Create custom types (only if they don't exist)
DO $$ BEGIN
  CREATE TYPE health_status_enum AS ENUM ('healthy', 'needs-water', 'needs-attention');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
  CREATE TYPE task_type_enum AS ENUM ('water', 'fertilize', 'repot', 'prune', 'pest-check', 'custom');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
  CREATE TYPE priority_enum AS ENUM ('low', 'medium', 'high');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
  CREATE TYPE growth_entry_type_enum AS ENUM ('new-leaf', 'new-branch', 'flowering', 'fruiting', 'growth-spurt', 'other');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  full_name text,
  avatar_url text,
  location text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create profiles policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;

CREATE POLICY "Users can read own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Create profiles updated_at trigger
DROP TRIGGER IF EXISTS handle_profiles_updated_at ON profiles;
CREATE TRIGGER handle_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Create plants table
CREATE TABLE IF NOT EXISTS plants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  name text NOT NULL,
  botanical_name text,
  image_url text NOT NULL,
  date_added timestamptz NOT NULL,
  last_watered timestamptz,
  last_repotted timestamptz,
  location text NOT NULL,
  pot_size text,
  soil_type text,
  notes text,
  health_status health_status_enum DEFAULT 'healthy',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on plants
ALTER TABLE plants ENABLE ROW LEVEL SECURITY;

-- Create plants policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Users can read own plants" ON plants;
DROP POLICY IF EXISTS "Users can insert own plants" ON plants;
DROP POLICY IF EXISTS "Users can update own plants" ON plants;
DROP POLICY IF EXISTS "Users can delete own plants" ON plants;

CREATE POLICY "Users can read own plants"
  ON plants
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own plants"
  ON plants
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own plants"
  ON plants
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own plants"
  ON plants
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create plants indexes
CREATE INDEX IF NOT EXISTS plants_user_id_idx ON plants(user_id);
CREATE INDEX IF NOT EXISTS plants_created_at_idx ON plants(created_at);

-- Create plants updated_at trigger
DROP TRIGGER IF EXISTS handle_plants_updated_at ON plants;
CREATE TRIGGER handle_plants_updated_at
  BEFORE UPDATE ON plants
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  plant_id uuid NOT NULL REFERENCES plants(id) ON DELETE CASCADE,
  plant_name text NOT NULL,
  title text NOT NULL,
  description text,
  type task_type_enum NOT NULL,
  due_date timestamptz NOT NULL,
  completed boolean DEFAULT false,
  completed_at timestamptz,
  recurring boolean DEFAULT false,
  recurring_interval integer,
  priority priority_enum DEFAULT 'medium',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on tasks
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- Create tasks policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Users can read own tasks" ON tasks;
DROP POLICY IF EXISTS "Users can insert own tasks" ON tasks;
DROP POLICY IF EXISTS "Users can update own tasks" ON tasks;
DROP POLICY IF EXISTS "Users can delete own tasks" ON tasks;

CREATE POLICY "Users can read own tasks"
  ON tasks
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tasks"
  ON tasks
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tasks"
  ON tasks
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tasks"
  ON tasks
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create tasks indexes
CREATE INDEX IF NOT EXISTS tasks_user_id_idx ON tasks(user_id);
CREATE INDEX IF NOT EXISTS tasks_plant_id_idx ON tasks(plant_id);
CREATE INDEX IF NOT EXISTS tasks_due_date_idx ON tasks(due_date);
CREATE INDEX IF NOT EXISTS tasks_completed_idx ON tasks(completed);

-- Create tasks updated_at trigger
DROP TRIGGER IF EXISTS handle_tasks_updated_at ON tasks;
CREATE TRIGGER handle_tasks_updated_at
  BEFORE UPDATE ON tasks
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Create growth_entries table
CREATE TABLE IF NOT EXISTS growth_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  plant_id uuid NOT NULL REFERENCES plants(id) ON DELETE CASCADE,
  date timestamptz NOT NULL,
  type growth_entry_type_enum NOT NULL,
  description text,
  height numeric,
  width numeric,
  leaf_count integer,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on growth_entries
ALTER TABLE growth_entries ENABLE ROW LEVEL SECURITY;

-- Create growth_entries policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Users can read own growth entries" ON growth_entries;
DROP POLICY IF EXISTS "Users can insert own growth entries" ON growth_entries;
DROP POLICY IF EXISTS "Users can update own growth entries" ON growth_entries;
DROP POLICY IF EXISTS "Users can delete own growth entries" ON growth_entries;

CREATE POLICY "Users can read own growth entries"
  ON growth_entries
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own growth entries"
  ON growth_entries
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own growth entries"
  ON growth_entries
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own growth entries"
  ON growth_entries
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create growth_entries indexes
CREATE INDEX IF NOT EXISTS growth_entries_user_id_idx ON growth_entries(user_id);
CREATE INDEX IF NOT EXISTS growth_entries_plant_id_idx ON growth_entries(plant_id);
CREATE INDEX IF NOT EXISTS growth_entries_date_idx ON growth_entries(date);

-- Create growth_entries updated_at trigger
DROP TRIGGER IF EXISTS handle_growth_entries_updated_at ON growth_entries;
CREATE TRIGGER handle_growth_entries_updated_at
  BEFORE UPDATE ON growth_entries
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Create plant_analyses table
CREATE TABLE IF NOT EXISTS plant_analyses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  plant_id uuid NOT NULL REFERENCES plants(id) ON DELETE CASCADE,
  analysis_date timestamptz NOT NULL,
  image_url text NOT NULL,
  health_score numeric NOT NULL CHECK (health_score >= 0 AND health_score <= 100),
  issues jsonb DEFAULT '[]'::jsonb,
  recommendations jsonb DEFAULT '[]'::jsonb,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on plant_analyses
ALTER TABLE plant_analyses ENABLE ROW LEVEL SECURITY;

-- Create plant_analyses policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Users can read own plant analyses" ON plant_analyses;
DROP POLICY IF EXISTS "Users can insert own plant analyses" ON plant_analyses;
DROP POLICY IF EXISTS "Users can update own plant analyses" ON plant_analyses;
DROP POLICY IF EXISTS "Users can delete own plant analyses" ON plant_analyses;

CREATE POLICY "Users can read own plant analyses"
  ON plant_analyses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own plant analyses"
  ON plant_analyses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own plant analyses"
  ON plant_analyses
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own plant analyses"
  ON plant_analyses
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create plant_analyses indexes
CREATE INDEX IF NOT EXISTS plant_analyses_user_id_idx ON plant_analyses(user_id);
CREATE INDEX IF NOT EXISTS plant_analyses_plant_id_idx ON plant_analyses(plant_id);
CREATE INDEX IF NOT EXISTS plant_analyses_analysis_date_idx ON plant_analyses(analysis_date);

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();