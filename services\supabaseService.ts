import { supabase } from '@/lib/supabase';
import { Plant, Task, GrowthEntry, PlantAnalysis } from '@/types';
import { Database } from '@/types/database';

type PlantRow = Database['public']['Tables']['plants']['Row'];
type TaskRow = Database['public']['Tables']['tasks']['Row'];
type GrowthEntryRow = Database['public']['Tables']['growth_entries']['Row'];
type PlantAnalysisRow = Database['public']['Tables']['plant_analyses']['Row'];

// Helper functions to convert between database rows and app types
const convertPlantFromDB = (row: PlantRow): Plant => ({
  id: row.id,
  name: row.name,
  botanicalName: row.botanical_name || undefined,
  image: row.image_url,
  dateAdded: row.date_added,
  lastWatered: row.last_watered || undefined,
  lastRepotted: row.last_repotted || undefined,
  location: row.location,
  potSize: row.pot_size || undefined,
  soilType: row.soil_type || undefined,
  notes: row.notes || undefined,
  healthStatus: row.health_status,
  photos: [], // TODO: Implement photo storage
});

const convertPlantToDB = (plant: Plant, userId: string): Database['public']['Tables']['plants']['Insert'] => ({
  id: plant.id,
  user_id: userId,
  name: plant.name,
  botanical_name: plant.botanicalName || null,
  image_url: plant.image,
  date_added: plant.dateAdded,
  last_watered: plant.lastWatered || null,
  last_repotted: plant.lastRepotted || null,
  location: plant.location,
  pot_size: plant.potSize || null,
  soil_type: plant.soilType || null,
  notes: plant.notes || null,
  health_status: plant.healthStatus || 'healthy',
});

const convertTaskFromDB = (row: TaskRow): Task => ({
  id: row.id,
  plantId: row.plant_id,
  plantName: row.plant_name,
  title: row.title,
  description: row.description || undefined,
  type: row.type,
  dueDate: row.due_date,
  completed: row.completed,
  completedAt: row.completed_at || undefined,
  recurring: row.recurring,
  recurringInterval: row.recurring_interval || undefined,
  priority: row.priority,
  createdAt: row.created_at,
});

const convertTaskToDB = (task: Omit<Task, 'id' | 'createdAt'>, userId: string): Database['public']['Tables']['tasks']['Insert'] => ({
  user_id: userId,
  plant_id: task.plantId,
  plant_name: task.plantName,
  title: task.title,
  description: task.description || null,
  type: task.type,
  due_date: task.dueDate,
  completed: task.completed,
  completed_at: task.completedAt || null,
  recurring: task.recurring || false,
  recurring_interval: task.recurringInterval || null,
  priority: task.priority || 'medium',
});

const convertGrowthEntryFromDB = (row: GrowthEntryRow): GrowthEntry => ({
  id: row.id,
  plantId: row.plant_id,
  date: row.date,
  type: row.type,
  description: row.description || undefined,
  measurements: {
    height: row.height || undefined,
    width: row.width || undefined,
    leafCount: row.leaf_count || undefined,
  },
});

const convertGrowthEntryToDB = (entry: Omit<GrowthEntry, 'id'>, userId: string): Database['public']['Tables']['growth_entries']['Insert'] => ({
  user_id: userId,
  plant_id: entry.plantId,
  date: entry.date,
  type: entry.type,
  description: entry.description || null,
  height: entry.measurements?.height || null,
  width: entry.measurements?.width || null,
  leaf_count: entry.measurements?.leafCount || null,
});

// Authentication functions
export const signUp = async (email: string, password: string, fullName?: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  });

  if (error) throw error;
  return data;
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) throw error;
  
  // Ensure profile exists after sign in
  if (data.user) {
    await ensureProfileExists(data.user);
  }
  
  return data;
};

export const signOut = async () => {
  console.log('signOut function called');
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Supabase signOut error:', error);
      throw error;
    }
    console.log('Supabase signOut successful');

    // Clear any local storage or cached data if needed
    // This ensures a clean logout state
    return { success: true };
  } catch (error) {
    console.error('Error during signOut:', error);
    throw error;
  }
};

export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error('Error getting current user:', error);
      // Don't throw error for auth errors, just return null
      if (error.message?.includes('session_not_found') || error.message?.includes('invalid_token')) {
        return null;
      }
      throw error;
    }
    return user;
  } catch (error) {
    console.error('Exception in getCurrentUser:', error);
    return null;
  }
};

// Helper function to ensure user profile exists
const ensureProfileExists = async (user: any) => {
  try {
    // Check if profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (fetchError && fetchError.code === 'PGRST116') {
      // Profile doesn't exist, create it
      console.log('Creating profile for user:', user.id);
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email,
          full_name: user.user_metadata?.full_name || null,
        });

      if (insertError) {
        console.error('Error creating profile:', insertError);
      } else {
        console.log('Profile created successfully');
      }
    } else if (fetchError) {
      console.error('Error checking profile:', fetchError);
    }
  } catch (error) {
    console.error('Error in ensureProfileExists:', error);
  }
};

// Plant operations
export const getPlants = async (): Promise<Plant[]> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  const { data, error } = await supabase
    .from('plants')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data.map(convertPlantFromDB);
};

export const savePlant = async (plant: Plant, isNewPlant: boolean = false): Promise<void> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  // Check if plant exists before saving (only if not explicitly marked as new)
  let shouldCreateTasks = isNewPlant;
  if (!isNewPlant) {
    const existingPlant = await getPlantById(plant.id);
    shouldCreateTasks = !existingPlant;
  }

  const plantData = convertPlantToDB(plant, user.id);

  const { error } = await supabase
    .from('plants')
    .upsert(plantData);

  if (error) throw error;

  // Create initial tasks for new plants only
  if (shouldCreateTasks) {
    await createInitialTasks(plant, user.id);
  }
};

export const deletePlant = async (plantId: string): Promise<void> => {
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError) {
    console.error('Auth error:', authError);
    throw new Error('Authentication failed');
  }
  if (!user) {
    console.error('No user found');
    throw new Error('User not authenticated');
  }

  console.log('Deleting plant:', plantId, 'for user:', user.id);
  
  // First, let's check if the plant exists and belongs to the user
  const { data: existingPlant, error: fetchError } = await supabase
    .from('plants')
    .select('id, user_id')
    .eq('id', plantId)
    .single();
    
  if (fetchError) {
    console.error('Error fetching plant:', fetchError);
    if (fetchError.code === 'PGRST116') {
      throw new Error('Plant not found');
    }
    throw new Error(`Database error: ${fetchError.message}`);
  }
  
  if (!existingPlant) {
    console.error('Plant not found in database');
    throw new Error('Plant not found');
  }
  
  console.log('Found plant:', existingPlant);
  
  if (existingPlant.user_id !== user.id) {
    console.error('Plant does not belong to user');
    throw new Error('Unauthorized to delete this plant');
  }

  // Delete the plant - related records should be deleted automatically due to CASCADE
  const { data: deletedData, error } = await supabase
    .from('plants')
    .delete()
    .eq('id', plantId)
    .eq('user_id', user.id)
    .select();

  if (error) {
    console.error('Error deleting plant:', error);
    throw new Error(`Failed to delete plant: ${error.message}`);
  }

  console.log('Plant deleted successfully:', deletedData);
};

export const getPlantById = async (plantId: string): Promise<Plant | null> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  const { data, error } = await supabase
    .from('plants')
    .select('*')
    .eq('id', plantId)
    .eq('user_id', user.id)
    .maybeSingle();

  if (error) {
    throw error;
  }

  if (!data) return null;

  return convertPlantFromDB(data);
};

// Task operations
export const getTasks = async (): Promise<Task[]> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  const { data, error } = await supabase
    .from('tasks')
    .select('*')
    .eq('user_id', user.id)
    .order('due_date', { ascending: true });

  if (error) throw error;
  return data.map(convertTaskFromDB);
};

export const createTask = async (task: Omit<Task, 'id' | 'createdAt'>): Promise<Task> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  const taskData = convertTaskToDB(task, user.id);

  const { data, error } = await supabase
    .from('tasks')
    .insert(taskData)
    .select()
    .single();

  if (error) throw error;
  return convertTaskFromDB(data);
};

export const completeTask = async (taskId: string): Promise<void> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  // Get the task first
  const { data: task, error: fetchError } = await supabase
    .from('tasks')
    .select('*')
    .eq('id', taskId)
    .eq('user_id', user.id)
    .single();

  if (fetchError) throw fetchError;

  // Mark task as completed
  const { error: updateError } = await supabase
    .from('tasks')
    .update({
      completed: true,
      completed_at: new Date().toISOString(),
    })
    .eq('id', taskId)
    .eq('user_id', user.id);

  if (updateError) throw updateError;

  // If it's a recurring task, create next occurrence
  if (task.recurring && task.recurring_interval) {
    const nextDueDate = new Date(task.due_date);
    nextDueDate.setDate(nextDueDate.getDate() + task.recurring_interval);

    const nextTaskData = {
      ...task,
      id: undefined,
      due_date: nextDueDate.toISOString(),
      completed: false,
      completed_at: null,
      created_at: undefined,
      updated_at: undefined,
    };

    const { error: insertError } = await supabase
      .from('tasks')
      .insert(nextTaskData);

    if (insertError) throw insertError;
  }
};

// Growth entry operations
export const getGrowthEntries = async (plantId?: string): Promise<GrowthEntry[]> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  let query = supabase
    .from('growth_entries')
    .select('*')
    .eq('user_id', user.id);

  if (plantId) {
    query = query.eq('plant_id', plantId);
  }

  const { data, error } = await query.order('date', { ascending: false });

  if (error) throw error;
  return data.map(convertGrowthEntryFromDB);
};

export const addGrowthEntry = async (entry: Omit<GrowthEntry, 'id'>): Promise<GrowthEntry> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  const entryData = convertGrowthEntryToDB(entry, user.id);

  const { data, error } = await supabase
    .from('growth_entries')
    .insert(entryData)
    .select()
    .single();

  if (error) throw error;
  return convertGrowthEntryFromDB(data);
};

// Plant analysis operations
export const savePlantAnalysis = async (analysis: PlantAnalysis): Promise<void> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  const { error } = await supabase
    .from('plant_analyses')
    .insert({
      user_id: user.id,
      plant_id: analysis.plantId,
      analysis_date: analysis.analysisDate,
      image_url: analysis.imageUrl,
      health_score: analysis.healthScore,
      issues: analysis.issues,
      recommendations: analysis.recommendations,
    });

  if (error) throw error;
};

export const getPlantAnalyses = async (plantId: string): Promise<PlantAnalysis[]> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  const { data, error } = await supabase
    .from('plant_analyses')
    .select('*')
    .eq('plant_id', plantId)
    .eq('user_id', user.id)
    .order('analysis_date', { ascending: false });

  if (error) throw error;

  return data.map((row: PlantAnalysisRow): PlantAnalysis => ({
    plantId: row.plant_id,
    analysisDate: row.analysis_date,
    imageUrl: row.image_url,
    healthScore: row.health_score,
    issues: row.issues as PlantAnalysis['issues'],
    recommendations: row.recommendations as string[],
  }));
};

// Helper function to create initial tasks for new plants
const createInitialTasks = async (plant: Plant, userId: string): Promise<void> => {
  // No longer create initial tasks automatically
  // Users can add tasks manually using the "Add Task" button
  console.log('Plant created without initial tasks - user can add tasks manually');
};

// Initialize example data for new users
export const initializeExampleData = async (): Promise<void> => {
  const user = await getCurrentUser();
  if (!user) throw new Error('User not authenticated');

  // Check if user already has plants
  const existingPlants = await getPlants();
  if (existingPlants.length > 0) return;

  // Create example plants
  const examplePlants: Plant[] = [
    {
      id: `plant_${Date.now()}_1`,
      name: 'Monstera Deliciosa',
      botanicalName: 'Monstera deliciosa',
      image: 'https://images.pexels.com/photos/6208086/pexels-photo-6208086.jpeg?auto=compress&cs=tinysrgb&w=400',
      dateAdded: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      lastWatered: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      location: 'Living room window',
      potSize: '8 inches',
      soilType: 'Well-draining potting mix',
      notes: 'Beautiful fenestrations developing. Loves the bright indirect light.',
      healthStatus: 'healthy',
      photos: [],
    },
    {
      id: `plant_${Date.now()}_2`,
      name: 'Snake Plant',
      botanicalName: 'Sansevieria trifasciata',
      image: 'https://images.pexels.com/photos/2123482/pexels-photo-2123482.jpeg?auto=compress&cs=tinysrgb&w=400',
      dateAdded: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
      lastWatered: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
      location: 'Bedroom corner',
      potSize: '6 inches',
      soilType: 'Cactus mix',
      notes: 'Very low maintenance. Perfect for beginners.',
      healthStatus: 'needs-water',
      photos: [],
    },
    {
      id: `plant_${Date.now()}_3`,
      name: 'Golden Pothos',
      botanicalName: 'Epipremnum aureum',
      image: 'https://images.pexels.com/photos/4751978/pexels-photo-4751978.jpeg?auto=compress&cs=tinysrgb&w=400',
      dateAdded: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
      lastWatered: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      location: 'Kitchen shelf',
      potSize: '5 inches',
      soilType: 'Regular potting soil',
      notes: 'Growing beautifully! Considering propagating some cuttings.',
      healthStatus: 'healthy',
      photos: [],
    },
  ];

  // Save example plants
  for (const plant of examplePlants) {
    await savePlant(plant);
  }

  // Add some example growth entries
  const growthEntries: Omit<GrowthEntry, 'id'>[] = [
    {
      plantId: examplePlants[0].id,
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      type: 'new-leaf',
      description: 'New leaf with beautiful fenestrations!',
      measurements: { leafCount: 12 },
    },
    {
      plantId: examplePlants[2].id,
      date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      type: 'new-branch',
      description: 'New vine starting to trail down',
    },
  ];

  for (const entry of growthEntries) {
    await addGrowthEntry(entry);
  }
};