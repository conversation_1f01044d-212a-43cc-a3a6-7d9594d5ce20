/*
  # Create plants table

  1. New Tables
    - `plants`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to profiles)
      - `name` (text)
      - `botanical_name` (text, optional)
      - `image_url` (text)
      - `date_added` (timestamp)
      - `last_watered` (timestamp, optional)
      - `last_repotted` (timestamp, optional)
      - `location` (text)
      - `pot_size` (text, optional)
      - `soil_type` (text, optional)
      - `notes` (text, optional)
      - `health_status` (enum: healthy, needs-water, needs-attention)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `plants` table
    - Add policies for authenticated users to manage their own plants
*/

-- Create health status enum
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'health_status_enum') THEN
    CREATE TYPE health_status_enum AS ENUM ('healthy', 'needs-water', 'needs-attention');
  END IF;
END $$;

CREATE TABLE IF NOT EXISTS plants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name text NOT NULL,
  botanical_name text,
  image_url text NOT NULL,
  date_added timestamptz NOT NULL,
  last_watered timestamptz,
  last_repotted timestamptz,
  location text NOT NULL,
  pot_size text,
  soil_type text,
  notes text,
  health_status health_status_enum DEFAULT 'healthy',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE plants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own plants"
  ON plants
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own plants"
  ON plants
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own plants"
  ON plants
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own plants"
  ON plants
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at timestamp
CREATE TRIGGER handle_plants_updated_at
  BEFORE UPDATE ON plants
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- Index for better performance
CREATE INDEX IF NOT EXISTS plants_user_id_idx ON plants(user_id);
CREATE INDEX IF NOT EXISTS plants_created_at_idx ON plants(created_at);