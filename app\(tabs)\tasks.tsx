import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Calendar } from 'react-native-calendars';
import { CircleCheck as CheckCircle, Circle, Plus, Filter } from 'lucide-react-native';
import { getTasks, completeTask, createTask } from '@/services/dataService';
import { Task } from '@/types';
import { useCustomDialog } from '@/components/CustomDialog';
import { TaskDetailsModal } from '@/components/TaskDetailsModal';
import { formatDate } from '@/utils/dateUtils';

export default function TasksScreen() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filter, setFilter] = useState<'all' | 'pending' | 'completed'>('all');
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [modalTasks, setModalTasks] = useState<Task[]>([]);
  const { showDialog, DialogComponent } = useCustomDialog();

  const handleDayPress = (day: any) => {
    const dateString = day.dateString;
    setSelectedDate(dateString);

    // Get tasks for the selected date
    const tasksForDate = tasks.filter(task => {
      const taskDate = new Date(task.dueDate).toISOString().split('T')[0];
      return taskDate === dateString;
    });

    // If there are tasks for this date, show the modal
    if (tasksForDate.length > 0) {
      setModalTasks(tasksForDate);
      setShowTaskModal(true);
    }
  };

  const handleOpenTaskDetails = (taskId: string) => {
    // Find the task and show detailed information
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      showDialog(
        'Task Details',
        `Task: ${task.title}\nPlant: ${task.plantName}\nPriority: ${task.priority?.toUpperCase() || 'MEDIUM'}\nStatus: ${task.completed ? 'Completed' : 'Pending'}${task.description ? `\n\nDescription: ${task.description}` : ''}`,
        [{
          text: 'OK',
          onPress: () => {
            // Return to the task modal after closing the details dialog
            setShowTaskModal(true);
          }
        }],
        'info'
      );
    }
  };

  useEffect(() => {
    loadTasks();
  }, []);

  useEffect(() => {
    filterTasks();
  }, [tasks, selectedDate, filter]);

  const loadTasks = async () => {
    try {
      const tasksData = await getTasks();
      setTasks(tasksData);
    } catch (error) {
      console.error('Error loading tasks:', error);
    }
  };

  const filterTasks = () => {
    let filtered = tasks;

    // Filter by date
    if (selectedDate) {
      filtered = filtered.filter(task => {
        const taskDate = new Date(task.dueDate).toISOString().split('T')[0];
        return taskDate === selectedDate;
      });
    }

    // Filter by status
    if (filter === 'pending') {
      filtered = filtered.filter(task => !task.completed);
    } else if (filter === 'completed') {
      filtered = filtered.filter(task => task.completed);
    }

    setFilteredTasks(filtered);
  };

  const handleCompleteTask = async (taskId: string) => {
    try {
      await completeTask(taskId);
      loadTasks();
    } catch (error) {
      showDialog(
        'Error',
        'Failed to complete task',
        [{ text: 'OK', onPress: () => {} }],
        'error'
      );
    }
  };

  const getMarkedDates = () => {
    const marked: { [key: string]: any } = {};

    tasks.forEach(task => {
      const dateKey = new Date(task.dueDate).toISOString().split('T')[0];
      if (!marked[dateKey]) {
        marked[dateKey] = {
          marked: true,
          dots: [],
          // Add a subtle background for days with tasks to indicate they're clickable
          customStyles: {
            container: {
              backgroundColor: '#F0FDF4',
              borderRadius: 16,
            },
            text: {
              color: '#111827',
              fontWeight: '500',
            },
          },
        };
      }

      const color = task.completed ? '#10B981' : '#F59E0B';
      marked[dateKey].dots.push({ color });
    });

    // Highlight selected date
    if (marked[selectedDate]) {
      // If selected date has tasks, keep the custom styling but add selection
      marked[selectedDate] = {
        ...marked[selectedDate],
        selected: true,
        selectedColor: '#10B981',
        selectedTextColor: '#FFFFFF',
      };
    } else {
      // If selected date has no tasks, just show selection
      marked[selectedDate] = {
        selected: true,
        selectedColor: '#10B981',
        selectedTextColor: '#FFFFFF',
      };
    }

    return marked;
  };

  const getTaskTypeColor = (type: string) => {
    switch (type) {
      case 'water': return '#3B82F6';
      case 'fertilize': return '#F59E0B';
      case 'repot': return '#8B5CF6';
      case 'prune': return '#EF4444';
      case 'pest-check': return '#84CC16';
      default: return '#6B7280';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Tasks & Schedule</Text>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => {
            showDialog(
              'Filter Tasks',
              'Choose what to show',
              [
                { text: 'All', onPress: () => setFilter('all') },
                { text: 'Pending', onPress: () => setFilter('pending') },
                { text: 'Completed', onPress: () => setFilter('completed') },
                { text: 'Cancel', onPress: () => {}, style: 'cancel' },
              ],
              'info'
            );
          }}>
          <Filter size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>

      <View style={styles.calendarContainer}>
        <Calendar
          onDayPress={handleDayPress}
          markedDates={getMarkedDates()}
          theme={{
            backgroundColor: '#FFFFFF',
            calendarBackground: '#FFFFFF',
            textSectionTitleColor: '#6B7280',
            selectedDayBackgroundColor: '#10B981',
            selectedDayTextColor: '#FFFFFF',
            todayTextColor: '#10B981',
            dayTextColor: '#111827',
            textDisabledColor: '#D1D5DB',
            dotColor: '#10B981',
            selectedDotColor: '#FFFFFF',
            arrowColor: '#10B981',
            monthTextColor: '#111827',
            indicatorColor: '#10B981',
            textDayFontFamily: 'Inter-Regular',
            textMonthFontFamily: 'Inter-SemiBold',
            textDayHeaderFontFamily: 'Inter-Medium',
            textDayFontSize: 16,
            textMonthFontSize: 18,
            textDayHeaderFontSize: 14,
          }}
        />
        <Text style={styles.calendarHint}>
          💡 Tap on days with colored dots to see task details
        </Text>
      </View>

      <View style={styles.tasksSection}>
        <Text style={styles.sectionTitle}>
          Tasks for {formatDate(selectedDate)}
        </Text>
        
        <ScrollView style={styles.tasksList} showsVerticalScrollIndicator={false}>
          {filteredTasks.length > 0 ? (
            filteredTasks.map((task) => (
              <View key={task.id} style={styles.taskItem}>
                <TouchableOpacity
                  style={styles.taskCheckbox}
                  onPress={() => handleCompleteTask(task.id)}>
                  {task.completed ? (
                    <CheckCircle size={24} color="#10B981" />
                  ) : (
                    <Circle size={24} color="#6B7280" />
                  )}
                </TouchableOpacity>
                
                <View style={styles.taskContent}>
                  <Text style={[styles.taskTitle, task.completed && styles.completedTask]}>
                    {task.title}
                  </Text>
                  <Text style={styles.taskPlant}>{task.plantName}</Text>
                  {task.description && (
                    <Text style={styles.taskDescription}>{task.description}</Text>
                  )}
                </View>
                
                <View style={[styles.taskTypeIndicator, { backgroundColor: getTaskTypeColor(task.type) }]} />
              </View>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyTitle}>No tasks for this date</Text>
              <Text style={styles.emptySubtitle}>
                Select a different date or add a new task
              </Text>
            </View>
          )}
        </ScrollView>
      </View>

      <TouchableOpacity
        style={styles.fab}
        onPress={() => {
          showDialog(
            'Add Task',
            'This feature will be available soon!',
            [{ text: 'OK', onPress: () => {} }],
            'info'
          );
        }}>
        <Plus size={24} color="#FFFFFF" />
      </TouchableOpacity>

      {/* Custom Dialog Component */}
      <DialogComponent />

      {/* Task Details Modal */}
      <TaskDetailsModal
        visible={showTaskModal}
        onClose={() => setShowTaskModal(false)}
        onOpenTaskDetails={handleOpenTaskDetails}
        tasks={modalTasks}
        selectedDate={selectedDate}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  filterButton: {
    padding: 8,
  },
  calendarContainer: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    borderRadius: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  calendarHint: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  tasksSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  tasksList: {
    flex: 1,
  },
  taskItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  taskCheckbox: {
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 4,
  },
  completedTask: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  taskPlant: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  taskDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  taskTypeIndicator: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginLeft: 12,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    backgroundColor: '#10B981',
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
});