/**
 * Utility functions for handling dates consistently across the app
 * to avoid timezone conversion issues
 */

/**
 * Formats a date string for display, avoiding timezone conversion issues
 * @param dateString - ISO date string or date string
 * @returns Formatted date string in local format
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  // Add timezone offset to ensure we get the correct local date
  const localDate = new Date(date.getTime() + date.getTimezoneOffset() * 60000);
  return localDate.toLocaleDateString();
};

/**
 * Creates a Date object from a date string (YYYY-MM-DD) in local timezone
 * to avoid timezone conversion issues when storing dates
 * @param dateString - Date string in YYYY-MM-DD format
 * @param hour - Hour to set (default: 12 to avoid timezone issues)
 * @returns Date object in local timezone
 */
export const createLocalDate = (dateString: string, hour: number = 12): Date => {
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day, hour, 0, 0);
};

/**
 * Formats a date for display from a date string, ensuring correct local date
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Formatted date string
 */
export const formatLocalDate = (dateString: string): string => {
  const [year, month, day] = dateString.split('-').map(Number);
  const localDate = new Date(year, month - 1, day);
  return localDate.toLocaleDateString();
};

/**
 * Gets today's date in YYYY-MM-DD format
 * @returns Today's date string
 */
export const getTodayString = (): string => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

/**
 * Gets a date string for a date that is a certain number of days from today
 * @param daysFromToday - Number of days to add (can be negative)
 * @returns Date string in YYYY-MM-DD format
 */
export const getDateString = (daysFromToday: number): string => {
  const date = new Date();
  date.setDate(date.getDate() + daysFromToday);
  return date.toISOString().split('T')[0];
};
