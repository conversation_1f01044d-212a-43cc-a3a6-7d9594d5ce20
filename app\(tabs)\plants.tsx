import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useFocusEffect } from 'expo-router';
import { Plus, Search, MoveVertical as MoreVertical, CreditCard as Edit, Trash2, X } from 'lucide-react-native';
import { getPlants, deletePlant } from '@/services/dataService';
import { Plant } from '@/types';
import { useCustomDialog } from '@/components/CustomDialog';
import { useCallback } from 'react';

export default function PlantsScreen() {
  const [plants, setPlants] = useState<Plant[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPlants, setFilteredPlants] = useState<Plant[]>([]);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [plantToDelete, setPlantToDelete] = useState<string | null>(null);
  const router = useRouter();
  const { showDialog, DialogComponent } = useCustomDialog();

  useEffect(() => {
    loadPlants();
  }, []);

  // Reload plants when screen comes into focus (e.g., after adding a new plant)
  useFocusEffect(
    useCallback(() => {
      console.log('Plants screen focused - reloading plants');
      loadPlants();
    }, [])
  );

  useEffect(() => {
    filterPlants();
  }, [searchQuery, plants]);

  const loadPlants = async () => {
    try {
      const plantsData = await getPlants();
      setPlants(plantsData);
    } catch (error) {
      console.error('Error loading plants:', error);
    }
  };

  const filterPlants = () => {
    if (!searchQuery.trim()) {
      setFilteredPlants(plants);
    } else {
      const filtered = plants.filter(
        (plant) =>
          plant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          plant.botanicalName?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredPlants(filtered);
    }
  };

  const performDelete = async () => {
    if (!plantToDelete) return;

    console.log('Performing deletion for plantId:', plantToDelete);
    setShowDeleteDialog(false);

    try {
      console.log('Attempting to delete plant:', plantToDelete);
      await deletePlant(plantToDelete);
      console.log('Plant deleted successfully');
      await loadPlants();
      console.log('Plants reloaded');
    } catch (error) {
      console.error('Delete error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete plant';
      showDialog(
        'Error',
        errorMessage,
        [{ text: 'OK', onPress: () => {} }],
        'error'
      );
    } finally {
      setPlantToDelete(null);
    }
  };

  const handleDeletePlant = (plantId: string) => {
    console.log('handleDeletePlant called with plantId:', plantId);
    setPlantToDelete(plantId);
    setShowDeleteDialog(true);
  };

  const cancelDelete = () => {
    console.log('User cancelled deletion');
    setShowDeleteDialog(false);
    setPlantToDelete(null);
  };

  const getPlantStatus = (plant: Plant) => {
    const today = new Date();
    const lastWatered = new Date(plant.lastWatered || plant.dateAdded);
    const daysSinceWatered = Math.floor((today.getTime() - lastWatered.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysSinceWatered > 14) return 'needs-attention';
    if (daysSinceWatered > 7) return 'needs-water';
    return 'healthy';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'needs-water': return '#F59E0B';
      case 'needs-attention': return '#EF4444';
      default: return '#10B981';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>My Plants</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/add-plant')}>
          <Plus size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Search size={20} color="#6B7280" />
        <TextInput
          style={styles.searchInput}
          placeholder="Search plants..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#9CA3AF"
        />
      </View>

      <ScrollView style={styles.plantsList} showsVerticalScrollIndicator={false}>
        {filteredPlants.length > 0 ? (
          filteredPlants.map((plant) => {
            const status = getPlantStatus(plant);
            return (
              <TouchableOpacity
                key={plant.id}
                style={styles.plantCard}
                onPress={() => router.push(`/plant-details?id=${plant.id}`)}>
                <View style={styles.plantCardContent}>
                  <Image source={{ uri: plant.image }} style={styles.plantImage} />
                  <View style={styles.plantInfo}>
                    <View style={styles.plantHeader}>
                      <Text style={styles.plantName}>{plant.name}</Text>
                      <View style={[styles.statusDot, { backgroundColor: getStatusColor(status) }]} />
                    </View>
                    {plant.botanicalName && (
                      <Text style={styles.botanicalName}>{plant.botanicalName}</Text>
                    )}
                    <Text style={styles.plantLocation}>{plant.location}</Text>
                    <Text style={styles.plantDate}>
                      Added {new Date(plant.dateAdded).toLocaleDateString()}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.plantActions}>
                  <TouchableOpacity
                    style={styles.menuButton}
                    onPress={(e) => {
                      e.stopPropagation();
                      router.push(`/add-plant?id=${plant.id}`);
                    }}>
                    <Edit size={18} color="#6B7280" />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={(e) => {
                      e.stopPropagation();
                      console.log('Delete button pressed for plant:', plant.id);
                      handleDeletePlant(plant.id);
                    }}>
                    <Trash2 size={18} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            );
          })
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyTitle}>No plants found</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery ? 'Try a different search term' : 'Start your plant journey by adding your first plant!'}
            </Text>
            {!searchQuery && (
              <TouchableOpacity
                style={styles.emptyButton}
                onPress={() => router.push('/add-plant')}>
                <Text style={styles.emptyButtonText}>Add Your First Plant</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </ScrollView>

      {/* Custom Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <View style={styles.dialogOverlay}>
          <View style={styles.dialogContainer}>
            <Text style={styles.dialogTitle}>Delete Plant</Text>
            <Text style={styles.dialogMessage}>
              Are you sure you want to delete this plant? This action cannot be undone.
            </Text>
            <View style={styles.dialogButtons}>
              <TouchableOpacity style={styles.cancelButton} onPress={cancelDelete}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.dialogDeleteButton} onPress={performDelete}>
                <Trash2 size={16} color="#FFFFFF" />
                <Text style={styles.dialogDeleteButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Custom Dialog Component */}
      <DialogComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  addButton: {
    backgroundColor: '#10B981',
    borderRadius: 12,
    padding: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    marginLeft: 12,
  },
  plantsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  plantCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  plantCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  plantImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginRight: 16,
  },
  plantInfo: {
    flex: 1,
  },
  plantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  plantName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  botanicalName: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  plantLocation: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  plantDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  plantActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  menuButton: {
    padding: 8,
    marginRight: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
  },
  deleteButton: {
    padding: 8,
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: '#10B981',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  emptyButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  // Delete dialog styles
  dialogOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  dialogContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    margin: 20,
    minWidth: 280,
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  dialogTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  dialogMessage: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 24,
  },
  dialogButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  dialogDeleteButton: {
    flex: 1,
    backgroundColor: '#EF4444',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 6,
  },
  dialogDeleteButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
});