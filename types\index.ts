export interface Plant {
  id: string;
  name: string;
  botanicalName?: string;
  image: string;
  dateAdded: string;
  lastWatered?: string;
  lastRepotted?: string;
  location: string;
  potSize?: string;
  soilType?: string;
  notes?: string;
  photos?: string[];
  healthStatus?: 'healthy' | 'needs-water' | 'needs-attention';
}

export interface Task {
  id: string;
  plantId: string;
  plantName: string;
  title: string;
  description?: string;
  type: 'water' | 'fertilize' | 'repot' | 'prune' | 'pest-check' | 'custom';
  dueDate: string;
  completed: boolean;
  recurring?: boolean;
  recurringInterval?: number; // days
  priority?: 'low' | 'medium' | 'high';
  createdAt: string;
  completedAt?: string;
}

export interface GrowthEntry {
  id: string;
  plantId: string;
  date: string;
  type: 'new-leaf' | 'new-branch' | 'flowering' | 'fruiting' | 'growth-spurt' | 'other';
  description?: string;
  photos?: string[];
  measurements?: {
    height?: number;
    width?: number;
    leafCount?: number;
  };
}

export interface PlantAnalysis {
  plantId: string;
  analysisDate: string;
  imageUrl: string;
  healthScore: number; // 0-100
  issues: {
    type: 'yellowing' | 'browning' | 'pests' | 'disease' | 'nutrient-deficiency' | 'overwatering' | 'underwatering';
    severity: 'low' | 'medium' | 'high';
    confidence: number;
    description: string;
  }[];
  recommendations: string[];
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  location?: string;
  joinedDate: string;
  preferences: {
    notificationsEnabled: boolean;
    reminderTime: string;
    temperatureUnit: 'celsius' | 'fahrenheit';
    language: 'en' | 'ru';
  };
}

export interface AppData {
  plants: Plant[];
  tasks: Task[];
  growthEntries: GrowthEntry[];
  user: User;
  version: string;
  exportDate: string;
}