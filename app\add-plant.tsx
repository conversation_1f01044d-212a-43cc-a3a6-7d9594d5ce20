import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Camera, Save, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { v4 as uuidv4 } from 'uuid';
import { savePlant, getPlantById } from '@/services/dataService';
import { Plant } from '@/types';
import { useCustomDialog } from '@/components/CustomDialog';

export default function AddPlantScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id?: string }>();
  const isEditing = Boolean(id);
  const { showDialog, DialogComponent } = useCustomDialog();

  const [plantData, setPlantData] = useState<Partial<Plant>>({
    name: '',
    botanicalName: '',
    image: 'https://images.pexels.com/photos/1005058/pexels-photo-1005058.jpeg?auto=compress&cs=tinysrgb&w=400',
    location: '',
    potSize: '',
    soilType: '',
    notes: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isEditing && id) {
      loadPlant(id);
    }
  }, [isEditing, id]);

  const loadPlant = async (plantId: string) => {
    try {
      const plant = await getPlantById(plantId);
      if (plant) {
        setPlantData(plant);
      }
    } catch (error) {
      showDialog(
        'Error',
        'Failed to load plant data',
        [{ text: 'OK', onPress: () => {} }],
        'error'
      );
    }
  };

  const handleImagePicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setPlantData({ ...plantData, image: result.assets[0].uri });
    }
  };

  const handleSave = async () => {
    if (!plantData.name?.trim()) {
      showDialog(
        'Error',
        'Please enter a plant name',
        [{ text: 'OK', onPress: () => {} }],
        'error'
      );
      return;
    }

    setIsLoading(true);
    try {
      const plant: Plant = {
        id: isEditing ? id! : uuidv4(),
        name: plantData.name.trim(),
        botanicalName: plantData.botanicalName?.trim() || '',
        image: plantData.image || 'https://images.pexels.com/photos/1005058/pexels-photo-1005058.jpeg?auto=compress&cs=tinysrgb&w=400',
        dateAdded: isEditing ? (plantData as Plant).dateAdded : new Date().toISOString(),
        location: plantData.location?.trim() || '',
        potSize: plantData.potSize?.trim() || '',
        soilType: plantData.soilType?.trim() || '',
        notes: plantData.notes?.trim() || '',
        lastWatered: (plantData as Plant).lastWatered,
        lastRepotted: (plantData as Plant).lastRepotted,
        photos: (plantData as Plant).photos || [],
        healthStatus: (plantData as Plant).healthStatus || 'healthy',
      };

      await savePlant(plant);
      router.back();
    } catch (error) {
      showDialog(
        'Error',
        'Failed to save plant',
        [{ text: 'OK', onPress: () => {} }],
        'error'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <X size={24} color="#6B7280" />
          </TouchableOpacity>
          <Text style={styles.title}>
            {isEditing ? 'Edit Plant' : 'Add New Plant'}
          </Text>
          <TouchableOpacity
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={isLoading}>
            <Save size={20} color="#FFFFFF" />
            <Text style={styles.saveButtonText}>
              {isLoading ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
          <View style={styles.imageSection}>
            <TouchableOpacity style={styles.imageContainer} onPress={handleImagePicker}>
              <Image source={{ uri: plantData.image }} style={styles.plantImage} />
              <View style={styles.imageOverlay}>
                <Camera size={24} color="#FFFFFF" />
                <Text style={styles.imageOverlayText}>Change Photo</Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.inputSection}>
            <Text style={styles.sectionTitle}>Plant Details</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Plant Name *</Text>
              <TextInput
                style={styles.input}
                value={plantData.name}
                onChangeText={(text) => setPlantData({ ...plantData, name: text })}
                placeholder="e.g., My Monstera"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Botanical Name</Text>
              <TextInput
                style={styles.input}
                value={plantData.botanicalName}
                onChangeText={(text) => setPlantData({ ...plantData, botanicalName: text })}
                placeholder="e.g., Monstera deliciosa"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Location</Text>
              <TextInput
                style={styles.input}
                value={plantData.location}
                onChangeText={(text) => setPlantData({ ...plantData, location: text })}
                placeholder="e.g., Living room window"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Pot Size</Text>
              <TextInput
                style={styles.input}
                value={plantData.potSize}
                onChangeText={(text) => setPlantData({ ...plantData, potSize: text })}
                placeholder="e.g., 6 inches"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Soil Type</Text>
              <TextInput
                style={styles.input}
                value={plantData.soilType}
                onChangeText={(text) => setPlantData({ ...plantData, soilType: text })}
                placeholder="e.g., Well-draining potting mix"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Notes</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={plantData.notes}
                onChangeText={(text) => setPlantData({ ...plantData, notes: text })}
                placeholder="Any special notes about your plant..."
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Custom Dialog Component */}
      <DialogComponent />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  cancelButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 4,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  form: {
    flex: 1,
  },
  imageSection: {
    alignItems: 'center',
    paddingVertical: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  imageContainer: {
    position: 'relative',
  },
  plantImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageOverlayText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
    marginTop: 4,
  },
  inputSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  textArea: {
    height: 80,
    paddingTop: 12,
  },
});