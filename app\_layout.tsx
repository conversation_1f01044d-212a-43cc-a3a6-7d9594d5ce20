import React, { useEffect, useState, useRef } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { useFonts } from 'expo-font';
import {
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
  Inter_700Bold,
} from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
import { supabase } from '@/lib/supabase';
import { Session } from '@supabase/supabase-js';
import { useRouter, useSegments } from 'expo-router';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { logAuthState, logAuthEvent, logNavigationAttempt } from '@/utils/authDebug';

SplashScreen.preventAutoHideAsync();

// Helper function to ensure user profile exists
const ensureProfileExists = async (user: any) => {
  try {
    // Check if profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (fetchError && fetchError.code === 'PGRST116') {
      // Profile doesn't exist, create it
      console.log('Creating profile for user:', user.id);
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email,
          full_name: user.user_metadata?.full_name || null,
        });

      if (insertError) {
        console.error('Error creating profile:', insertError);
      } else {
        console.log('Profile created successfully');
      }
    } else if (fetchError) {
      console.error('Error checking profile:', fetchError);
    }
  } catch (error) {
    console.error('Error in ensureProfileExists:', error);
  }
};

export default function RootLayout() {
  useFrameworkReady();
  
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const segments = useSegments();
  const mounted = useRef(true);
  
  const [fontsLoaded, fontError] = useFonts({
    'Inter-Regular': Inter_400Regular,
    'Inter-Medium': Inter_500Medium,
    'Inter-SemiBold': Inter_600SemiBold,
    'Inter-Bold': Inter_700Bold,
  });

  useEffect(() => {
    mounted.current = true;
    
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (mounted.current) {
        setSession(session);
        // Ensure profile exists for existing session
        if (session?.user) {
          ensureProfileExists(session.user);
        }
        setIsLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (mounted.current) {
        logAuthEvent(event, session);
        setSession(session);

        // Ensure profile exists when session changes (only for sign in events)
        if (session?.user && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
          ensureProfileExists(session.user);
        }

        // Handle sign out event
        if (event === 'SIGNED_OUT') {
          console.log('User signed out, clearing session');
          setSession(null);
        }
      }
    });

    return () => {
      mounted.current = false;
      subscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  useEffect(() => {
    if (isLoading || !mounted.current) return;

    const inAuthGroup = segments[0] === 'auth';

    logAuthState('Navigation effect', session, segments);

    if (session && inAuthGroup) {
      // User is signed in and on auth screen, redirect to main app
      logNavigationAttempt('/auth', '/(tabs)', 'User signed in');
      router.replace('/(tabs)');
    } else if (!session && !inAuthGroup) {
      // User is not signed in and not on auth screen, redirect to auth
      logNavigationAttempt(segments.join('/'), '/auth', 'User not signed in');
      router.replace('/auth');
    }
  }, [session, segments, isLoading, router]);

  if (!fontsLoaded && !fontError) {
    return null;
  }

  if (isLoading) {
    return null;
  }

  return (
    <ErrorBoundary>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="(tabs)" />
        <Stack.Screen name="auth" />
        <Stack.Screen name="plant-details" />
        <Stack.Screen name="add-plant" />
        <Stack.Screen name="camera" />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ErrorBoundary>
  );
}